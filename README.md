# Agent Development Kit (ADK) Crash Course

This repository contains examples for learning Google's Agent Development Kit (ADK), a powerful framework for building LLM-powered agents.

## Getting Started

### Setup Environment

You only need to create one virtual environment for all examples in this course. Follow these steps to set it up:

```bash
# Create virtual environment in the root directory
python -m venv .venv

# Activate (each new terminal)
# macOS/Linux:
source .venv/bin/activate
# Windows CMD:
.venv\Scripts\activate.bat
# Windows PowerShell:
.venv\Scripts\Activate.ps1

# Install dependencies
pip install -r requirements.txt
```

Once set up, this single environment will work for all examples in the repository.

### Setting Up API Keys

1. Create an account in Google Cloud https://cloud.google.com/?hl=en
2. Create a new project
3. Go to https://aistudio.google.com/apikey
4. Create an API key
5. Assign key to the project
6. Connect to a billing account

Each example folder contains a `.env.example` file. For each project you want to run:

1. Navigate to the example folder
2. Rename `.env.example` to `.env` 
3. Open the `.env` file and replace the placeholder with your API key:
   ```
   GOOGLE_API_KEY=your_api_key_here
   ```

You'll need to repeat this for each example project you want to run.

## Examples Overview

Here's what you can learn from each example folder:

### 1. Basic Agent
Introduction to the simplest form of ADK agents. Learn how to create a basic agent that can respond to user queries.

### 2. Tool Agent
Learn how to enhance agents with tools that allow them to perform actions beyond just generating text.

### 3. LiteLLM Agent
Example of using LiteLLM to abstract away LLM provider details and easily switch between different models.

### 4. Structured Outputs
Learn how to use Pydantic models with `output_schema` to ensure consistent, structured responses from your agents.

### 5. Sessions and State
Understand how to maintain state and memory across multiple interactions using sessions.

### 6. Persistent Storage
Learn techniques for storing agent data persistently across sessions and application restarts.

### 7. Multi-Agent
See how to orchestrate multiple specialized agents working together to solve complex tasks.

### 8. Stateful Multi-Agent
Build agents that maintain and update state throughout complex multi-turn conversations.

### 9. Callbacks
Implement event callbacks to monitor and respond to agent behaviors in real-time.

### 10. Sequential Agent
Create pipeline workflows where agents operate in a defined sequence to process information.

### 11. Parallel Agent
Leverage concurrent operations with parallel agents for improved efficiency and performance.

### 12. Loop Agent
Build sophisticated agents that can iteratively refine their outputs through feedback loops.

### 13. Streaming Agent
Learn how to implement real-time streaming responses for better user experience with long-running tasks.

### 14. Webhook Agent
Understand how to handle webhooks and integrate with external services for event-driven architectures.

### 15. File Processing Agent
Master file upload, processing, and manipulation capabilities for document-based workflows.

### 16. Database Agent
Explore database integration patterns for persistent data storage and complex queries.

### 17. API Integration Agent
Learn best practices for integrating with external APIs and handling various response formats.

### 18. Custom Model Agent
Discover how to use custom or fine-tuned models beyond the standard Gemini offerings.

### 19. Conversation Memory Agent
Implement advanced conversation memory patterns for context-aware, long-term interactions.

### 20. Validation Agent
Build robust input validation and error handling mechanisms for production-ready agents.

### 21. Scheduling Agent
Create agents that can handle task scheduling, automation, and time-based operations.

### 22. Multimodal Agent
Work with images, documents, and other media types for comprehensive multimodal AI applications.

## Event-Driven Examples

The `event-driven-examples/` folder contains agents that demonstrate reactive programming patterns, real-time processing, and event-driven architectures:

### Core Event-Driven Patterns
- **Streaming Agent** - Real-time response streaming and progressive content delivery
- **Webhook Agent** - External service integration and event-driven workflows
- **File Processing Agent** - Event-driven file upload and processing workflows
- **Scheduling Agent** - Time-based triggers and automated task execution

### Real-World Event-Driven Applications
- **Real-Time Chat Moderation Agent** - Live chat content filtering and community management
- **IoT Sensor Monitoring Agent** - Real-time sensor data processing and anomaly detection

These examples focus on building responsive, scalable applications that react to external events and process real-time data streams.

## More Examples - Real-World Use Cases

The `more-examples/` folder contains industry-specific, production-ready agent implementations for real business scenarios:

### Business & Enterprise
- **Customer Support Agent** - Automated customer service with ticket routing and CRM integration
- **Sales Lead Qualification Agent** - Automated lead scoring and qualification workflows
- **Invoice Processing Agent** - OCR-based invoice processing and approval automation
- **HR Recruitment Agent** - Resume screening and candidate evaluation systems

### E-commerce & Retail
- **Product Recommendation Agent** - Personalized product recommendations and cross-selling
- **Inventory Management Agent** - Automated stock monitoring and reordering systems
- **Price Optimization Agent** - Dynamic pricing based on market conditions and demand

### Financial Services
- **Fraud Detection Agent** - Real-time transaction monitoring and fraud prevention
- **Financial Planning Agent** - Personal financial advice and investment recommendations
- **Loan Underwriting Agent** - Automated loan application processing and risk assessment

### Healthcare & Life Sciences
- **Medical Appointment Agent** - Healthcare scheduling and patient management
- **Clinical Data Analysis Agent** - Medical record analysis and treatment insights

### Manufacturing & Operations
- **Predictive Maintenance Agent** - Equipment monitoring and maintenance scheduling
- **Quality Control Agent** - Automated quality inspection and compliance reporting

### Additional Industries
- Real Estate, Education, Logistics, Social Media, Security, and more specialized use cases

Each example in `more-examples/` includes production-ready patterns, real API integrations, and industry-specific workflows that can be directly adapted for business use.

## Official Documentation

For more detailed information, check out the official ADK documentation:
- https://google.github.io/adk-docs/get-started/quickstart

## Support

Need help or run into issues? Join our free AI Developer Accelerator community on Skool:
- [AI Developer Accelerator Community](https://www.skool.com/ai-developer-accelerator/about)

In the community you'll find:
- Weekly coaching and support calls
- Early access to code from YouTube projects
- A network of AI developers of all skill levels ready to help
- Behind-the-scenes looks at how these apps are built
