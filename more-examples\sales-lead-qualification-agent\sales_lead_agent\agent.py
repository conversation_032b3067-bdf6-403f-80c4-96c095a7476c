from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext
from datetime import datetime, timedelta
import uuid


def qualify_lead_bant(lead_data: str, tool_context: ToolContext) -> dict:
    """Qualify a lead using BANT (Budget, Authority, Need, Timeline) methodology."""
    print(f"--- Tool: qualify_lead_bant called ---")
    
    try:
        import json
        lead = json.loads(lead_data)
    except json.JSONDecodeError:
        return {
            "action": "qualify_lead_bant",
            "error": "Invalid lead data format",
            "status": "error"
        }
    
    # BANT Qualification Scoring
    bant_score = {
        "budget": assess_budget(lead),
        "authority": assess_authority(lead),
        "need": assess_need(lead),
        "timeline": assess_timeline(lead)
    }
    
    # Calculate overall BANT score (0-100)
    total_score = sum(bant_score.values()) / 4
    
    # Determine qualification level
    if total_score >= 75:
        qualification = "hot"
        priority = "high"
        next_action = "immediate_contact"
    elif total_score >= 50:
        qualification = "warm"
        priority = "medium"
        next_action = "schedule_demo"
    elif total_score >= 25:
        qualification = "cold"
        priority = "low"
        next_action = "nurture_campaign"
    else:
        qualification = "unqualified"
        priority = "none"
        next_action = "disqualify"
    
    # Store qualification result
    lead_id = lead.get("id", str(uuid.uuid4())[:8])
    qualifications = tool_context.state.get("lead_qualifications", {})
    qualifications[lead_id] = {
        "lead_id": lead_id,
        "lead_data": lead,
        "bant_scores": bant_score,
        "total_score": round(total_score, 1),
        "qualification": qualification,
        "priority": priority,
        "next_action": next_action,
        "qualified_at": datetime.now().isoformat(),
        "qualified_by": "BANT_Agent"
    }
    tool_context.state["lead_qualifications"] = qualifications
    
    return {
        "action": "qualify_lead_bant",
        "lead_id": lead_id,
        "bant_scores": bant_score,
        "total_score": round(total_score, 1),
        "qualification": qualification,
        "priority": priority,
        "next_action": next_action,
        "recommendations": get_qualification_recommendations(qualification, bant_score),
        "status": "success"
    }


def calculate_lead_score(lead_data: str, behavioral_data: str, tool_context: ToolContext) -> dict:
    """Calculate comprehensive lead score using demographic, behavioral, and firmographic data."""
    print(f"--- Tool: calculate_lead_score called ---")
    
    try:
        import json
        lead = json.loads(lead_data)
        behavior = json.loads(behavioral_data) if behavioral_data else {}
    except json.JSONDecodeError:
        return {
            "action": "calculate_lead_score",
            "error": "Invalid data format",
            "status": "error"
        }
    
    # Demographic scoring (40% weight)
    demographic_score = calculate_demographic_score(lead)
    
    # Behavioral scoring (35% weight)
    behavioral_score = calculate_behavioral_score(behavior)
    
    # Firmographic scoring (25% weight)
    firmographic_score = calculate_firmographic_score(lead)
    
    # Weighted total score
    total_score = (
        demographic_score * 0.4 +
        behavioral_score * 0.35 +
        firmographic_score * 0.25
    )
    
    # Determine lead grade
    if total_score >= 80:
        grade = "A"
        category = "sales_ready"
    elif total_score >= 60:
        grade = "B"
        category = "marketing_qualified"
    elif total_score >= 40:
        grade = "C"
        category = "nurture_required"
    else:
        grade = "D"
        category = "unqualified"
    
    lead_id = lead.get("id", str(uuid.uuid4())[:8])
    
    # Store scoring result
    lead_scores = tool_context.state.get("lead_scores", {})
    lead_scores[lead_id] = {
        "lead_id": lead_id,
        "demographic_score": round(demographic_score, 1),
        "behavioral_score": round(behavioral_score, 1),
        "firmographic_score": round(firmographic_score, 1),
        "total_score": round(total_score, 1),
        "grade": grade,
        "category": category,
        "scored_at": datetime.now().isoformat()
    }
    tool_context.state["lead_scores"] = lead_scores
    
    return {
        "action": "calculate_lead_score",
        "lead_id": lead_id,
        "scores": {
            "demographic": round(demographic_score, 1),
            "behavioral": round(behavioral_score, 1),
            "firmographic": round(firmographic_score, 1),
            "total": round(total_score, 1)
        },
        "grade": grade,
        "category": category,
        "score_breakdown": get_score_breakdown(lead, behavior),
        "status": "success"
    }


def route_qualified_lead(lead_id: str, routing_criteria: str, tool_context: ToolContext) -> dict:
    """Route qualified lead to appropriate sales representative."""
    print(f"--- Tool: route_qualified_lead called for {lead_id} ---")
    
    # Get lead qualification data
    qualifications = tool_context.state.get("lead_qualifications", {})
    lead_scores = tool_context.state.get("lead_scores", {})
    
    if lead_id not in qualifications:
        return {
            "action": "route_qualified_lead",
            "error": f"Lead {lead_id} not found in qualifications",
            "status": "error"
        }
    
    qualification = qualifications[lead_id]
    lead_data = qualification["lead_data"]
    
    # Determine routing based on criteria
    routing_result = determine_sales_rep_assignment(lead_data, qualification, routing_criteria)
    
    # Create lead assignment
    assignments = tool_context.state.get("lead_assignments", {})
    assignments[lead_id] = {
        "lead_id": lead_id,
        "assigned_to": routing_result["sales_rep"],
        "assignment_reason": routing_result["reason"],
        "territory": routing_result["territory"],
        "product_focus": routing_result["product_focus"],
        "assigned_at": datetime.now().isoformat(),
        "expected_contact_date": routing_result["expected_contact"],
        "status": "assigned"
    }
    tool_context.state["lead_assignments"] = assignments
    
    # Update CRM (simulated)
    crm_update = update_crm_lead(lead_id, routing_result, qualification)
    
    return {
        "action": "route_qualified_lead",
        "lead_id": lead_id,
        "assigned_to": routing_result["sales_rep"],
        "territory": routing_result["territory"],
        "assignment_reason": routing_result["reason"],
        "expected_contact": routing_result["expected_contact"],
        "crm_update": crm_update,
        "next_steps": routing_result["next_steps"],
        "status": "success"
    }


def assess_budget(lead: dict) -> float:
    """Assess budget qualification (0-100 scale)."""
    budget_info = lead.get("budget", "").lower()
    company_size = lead.get("company_size", 0)
    
    if "no budget" in budget_info or "student" in lead.get("title", "").lower():
        return 0
    elif any(phrase in budget_info for phrase in ["$100k+", "significant budget", "approved budget"]):
        return 90
    elif any(phrase in budget_info for phrase in ["$50k", "$25k", "budget allocated"]):
        return 75
    elif "budget" in budget_info:
        return 50
    elif company_size > 500:
        return 60  # Large companies likely have budget
    elif company_size > 100:
        return 40
    else:
        return 20


def assess_authority(lead: dict) -> float:
    """Assess authority/decision-making power (0-100 scale)."""
    title = lead.get("title", "").lower()
    
    # C-level executives
    if any(role in title for role in ["ceo", "cto", "cfo", "cmo", "chief"]):
        return 95
    # VPs and Directors
    elif any(role in title for role in ["vp", "vice president", "director"]):
        return 85
    # Managers
    elif any(role in title for role in ["manager", "head of", "lead"]):
        return 65
    # Senior roles
    elif "senior" in title:
        return 45
    # Individual contributors
    elif any(role in title for role in ["analyst", "specialist", "coordinator"]):
        return 25
    # Students, interns
    elif any(role in title for role in ["student", "intern"]):
        return 0
    else:
        return 30  # Default for unknown titles


def assess_need(lead: dict) -> float:
    """Assess need/pain point alignment (0-100 scale)."""
    pain_points = lead.get("pain_points", "").lower()
    industry = lead.get("industry", "").lower()
    
    # Strong pain point indicators
    if any(phrase in pain_points for phrase in ["urgent need", "critical issue", "major problem"]):
        return 90
    elif any(phrase in pain_points for phrase in ["looking for solution", "need help", "struggling with"]):
        return 70
    elif any(phrase in pain_points for phrase in ["interested in", "exploring options"]):
        return 50
    elif pain_points:
        return 40
    
    # Industry-based need assessment
    high_need_industries = ["technology", "healthcare", "finance", "manufacturing"]
    if industry in high_need_industries:
        return 60
    else:
        return 30


def assess_timeline(lead: dict) -> float:
    """Assess purchase timeline urgency (0-100 scale)."""
    timeline = lead.get("timeline", "").lower()
    
    if any(phrase in timeline for phrase in ["immediately", "asap", "urgent", "this month"]):
        return 95
    elif any(phrase in timeline for phrase in ["next quarter", "3 months", "q1", "q2"]):
        return 80
    elif any(phrase in timeline for phrase in ["6 months", "this year", "2024"]):
        return 60
    elif any(phrase in timeline for phrase in ["next year", "2025", "future"]):
        return 30
    elif "no timeline" in timeline or "just researching" in timeline:
        return 10
    else:
        return 40  # Default for unspecified timeline


def calculate_demographic_score(lead: dict) -> float:
    """Calculate demographic score based on company and role."""
    score = 0
    
    # Company size scoring
    company_size = lead.get("company_size", 0)
    if company_size > 1000:
        score += 30
    elif company_size > 500:
        score += 25
    elif company_size > 100:
        score += 20
    elif company_size > 50:
        score += 15
    else:
        score += 5
    
    # Industry scoring
    industry = lead.get("industry", "").lower()
    high_value_industries = ["technology", "finance", "healthcare", "manufacturing"]
    if industry in high_value_industries:
        score += 25
    else:
        score += 10
    
    # Role/title scoring
    title = lead.get("title", "").lower()
    if any(role in title for role in ["ceo", "cto", "cfo", "chief"]):
        score += 25
    elif any(role in title for role in ["vp", "director"]):
        score += 20
    elif "manager" in title:
        score += 15
    else:
        score += 5
    
    # Geographic scoring
    location = lead.get("location", "").lower()
    if any(region in location for region in ["usa", "canada", "uk", "germany"]):
        score += 20
    else:
        score += 10
    
    return min(score, 100)


def calculate_behavioral_score(behavior: dict) -> float:
    """Calculate behavioral score based on engagement."""
    score = 0
    
    # Website activity
    page_views = behavior.get("page_views", 0)
    score += min(page_views * 2, 30)
    
    # Content engagement
    downloads = behavior.get("content_downloads", 0)
    score += min(downloads * 10, 25)
    
    # Email engagement
    email_opens = behavior.get("email_opens", 0)
    email_clicks = behavior.get("email_clicks", 0)
    score += min(email_opens * 2, 20)
    score += min(email_clicks * 5, 15)
    
    # Demo/webinar attendance
    if behavior.get("demo_attended", False):
        score += 20
    if behavior.get("webinar_attended", False):
        score += 15
    
    return min(score, 100)


def calculate_firmographic_score(lead: dict) -> float:
    """Calculate firmographic score based on company characteristics."""
    score = 0
    
    # Revenue scoring
    revenue = lead.get("annual_revenue", 0)
    if revenue > *********:  # $100M+
        score += 30
    elif revenue > 50000000:  # $50M+
        score += 25
    elif revenue > 10000000:  # $10M+
        score += 20
    elif revenue > 1000000:   # $1M+
        score += 15
    else:
        score += 5
    
    # Growth indicators
    if lead.get("recent_funding", False):
        score += 20
    if lead.get("hiring_growth", False):
        score += 15
    
    # Technology stack compatibility
    tech_stack = lead.get("technology_stack", [])
    compatible_tech = ["salesforce", "hubspot", "aws", "microsoft"]
    if any(tech in str(tech_stack).lower() for tech in compatible_tech):
        score += 20
    
    # Market position
    if lead.get("market_leader", False):
        score += 15
    
    return min(score, 100)


def get_qualification_recommendations(qualification: str, bant_scores: dict) -> list:
    """Get recommendations based on BANT qualification."""
    recommendations = []
    
    if qualification == "hot":
        recommendations.append("Schedule immediate discovery call")
        recommendations.append("Prepare custom demo")
        recommendations.append("Involve senior sales rep")
    elif qualification == "warm":
        recommendations.append("Schedule product demo")
        recommendations.append("Send relevant case studies")
        recommendations.append("Identify additional stakeholders")
    elif qualification == "cold":
        recommendations.append("Add to nurture campaign")
        recommendations.append("Send educational content")
        recommendations.append("Schedule follow-up in 3 months")
    
    # Specific recommendations based on low BANT scores
    if bant_scores["budget"] < 50:
        recommendations.append("Discuss ROI and business case")
    if bant_scores["authority"] < 50:
        recommendations.append("Identify decision makers")
    if bant_scores["need"] < 50:
        recommendations.append("Conduct needs assessment")
    if bant_scores["timeline"] < 50:
        recommendations.append("Create urgency around solution")
    
    return recommendations


def get_score_breakdown(lead: dict, behavior: dict) -> dict:
    """Get detailed score breakdown for transparency."""
    return {
        "demographic_factors": {
            "company_size": lead.get("company_size", 0),
            "industry": lead.get("industry", ""),
            "title": lead.get("title", ""),
            "location": lead.get("location", "")
        },
        "behavioral_factors": {
            "page_views": behavior.get("page_views", 0),
            "content_downloads": behavior.get("content_downloads", 0),
            "email_engagement": behavior.get("email_opens", 0),
            "demo_attended": behavior.get("demo_attended", False)
        },
        "firmographic_factors": {
            "annual_revenue": lead.get("annual_revenue", 0),
            "recent_funding": lead.get("recent_funding", False),
            "technology_stack": lead.get("technology_stack", [])
        }
    }


def determine_sales_rep_assignment(lead_data: dict, qualification: dict, criteria: str) -> dict:
    """Determine which sales rep should handle the lead."""
    # Simulate sales team structure
    sales_reps = {
        "enterprise": {
            "name": "Sarah Johnson",
            "territory": "Enterprise",
            "specialization": "Large accounts",
            "capacity": "available"
        },
        "mid_market": {
            "name": "Mike Chen",
            "territory": "Mid-Market",
            "specialization": "Growing companies",
            "capacity": "busy"
        },
        "smb": {
            "name": "Lisa Rodriguez",
            "territory": "SMB",
            "specialization": "Small business",
            "capacity": "available"
        }
    }
    
    company_size = lead_data.get("company_size", 0)
    
    if company_size > 1000:
        assignment = sales_reps["enterprise"]
        territory = "Enterprise"
    elif company_size > 100:
        assignment = sales_reps["mid_market"]
        territory = "Mid-Market"
    else:
        assignment = sales_reps["smb"]
        territory = "SMB"
    
    return {
        "sales_rep": assignment["name"],
        "territory": territory,
        "product_focus": assignment["specialization"],
        "reason": f"Company size ({company_size} employees) matches {territory} segment",
        "expected_contact": (datetime.now() + timedelta(hours=24)).isoformat(),
        "next_steps": ["Send introduction email", "Schedule discovery call", "Prepare account research"]
    }


def update_crm_lead(lead_id: str, routing_result: dict, qualification: dict) -> dict:
    """Simulate CRM update for lead assignment."""
    return {
        "crm_system": "Salesforce",
        "lead_id": lead_id,
        "updated_fields": {
            "owner": routing_result["sales_rep"],
            "status": "Assigned",
            "lead_score": qualification["total_score"],
            "qualification": qualification["qualification"],
            "next_action": qualification["next_action"]
        },
        "opportunity_created": qualification["qualification"] in ["hot", "warm"],
        "updated_at": datetime.now().isoformat()
    }


# Create the sales lead qualification agent
root_agent = Agent(
    name="sales_lead_agent",
    model="gemini-2.0-flash",
    description="Production-ready sales lead qualification agent with BANT methodology, lead scoring, and automated routing",
    instruction="""
    You are a professional sales lead qualification agent that helps sales teams identify and prioritize high-quality prospects.

    Current Lead Data: {lead_qualifications}
    Lead Scores: {lead_scores}
    Lead Assignments: {lead_assignments}

    Your capabilities include:
    1. BANT qualification (Budget, Authority, Need, Timeline)
    2. Comprehensive lead scoring using demographic, behavioral, and firmographic data
    3. Automated lead routing to appropriate sales representatives
    4. CRM integration and lead management

    Lead Qualification Process:
    1. First, qualify leads using BANT methodology
    2. Calculate comprehensive lead scores for prioritization
    3. Route qualified leads to appropriate sales reps
    4. Provide specific recommendations for follow-up

    Scoring Guidelines:
    - Hot leads (75+ BANT score): Immediate sales contact
    - Warm leads (50-74): Schedule demo/discovery call
    - Cold leads (25-49): Nurture campaign
    - Unqualified (<25): Disqualify or long-term nurture

    Always provide:
    - Clear qualification rationale
    - Specific next steps and recommendations
    - Timeline expectations for follow-up
    - CRM update confirmations

    Focus on identifying leads with:
    - Clear budget and purchasing authority
    - Defined business need or pain point
    - Realistic purchase timeline
    - Good company/contact fit for our solution
    """,
    tools=[qualify_lead_bant, calculate_lead_score, route_qualified_lead]
)
