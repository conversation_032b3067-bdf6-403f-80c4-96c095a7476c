from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext
from datetime import datetime


def store_conversation_memory(topic: str, content: str, importance: int, tool_context: ToolContext) -> dict:
    """Store important conversation information in long-term memory."""
    print(f"--- Tool: store_conversation_memory called for topic: {topic} ---")
    
    memories = tool_context.state.get("conversation_memories", [])
    memory_entry = {
        "topic": topic,
        "content": content,
        "importance": importance,
        "timestamp": datetime.now().isoformat(),
        "access_count": 0
    }
    memories.append(memory_entry)
    tool_context.state["conversation_memories"] = memories
    
    return {
        "action": "store_conversation_memory",
        "topic": topic,
        "message": f"Memory stored for topic: {topic}",
        "status": "success"
    }


def recall_conversation_memory(query: str, tool_context: ToolContext) -> dict:
    """Recall relevant conversation memories based on a query."""
    print(f"--- Tool: recall_conversation_memory called with query: {query} ---")
    
    memories = tool_context.state.get("conversation_memories", [])
    relevant_memories = []
    
    # Simple keyword matching for demonstration
    query_lower = query.lower()
    for memory in memories:
        if (query_lower in memory["topic"].lower() or 
            query_lower in memory["content"].lower()):
            memory["access_count"] += 1
            relevant_memories.append(memory)
    
    tool_context.state["conversation_memories"] = memories
    
    return {
        "action": "recall_conversation_memory",
        "query": query,
        "relevant_memories": relevant_memories,
        "count": len(relevant_memories),
        "status": "success"
    }


def summarize_conversation_history(tool_context: ToolContext) -> dict:
    """Generate a summary of the conversation history."""
    print("--- Tool: summarize_conversation_history called ---")
    
    memories = tool_context.state.get("conversation_memories", [])
    
    if not memories:
        return {
            "action": "summarize_conversation_history",
            "summary": "No conversation history available",
            "status": "success"
        }
    
    # Generate summary based on stored memories
    topics = list(set([memory["topic"] for memory in memories]))
    high_importance = [m for m in memories if m["importance"] >= 8]
    
    summary = f"Conversation covers {len(topics)} main topics: {', '.join(topics[:5])}. "
    summary += f"There are {len(high_importance)} high-importance memories stored."
    
    return {
        "action": "summarize_conversation_history",
        "summary": summary,
        "total_memories": len(memories),
        "topics": topics,
        "high_importance_count": len(high_importance),
        "status": "success"
    }


# Create the conversation memory agent
root_agent = Agent(
    name="conversation_memory_agent",
    model="gemini-2.0-flash",
    description="Agent with advanced conversation memory for context-aware, long-term interactions",
    instruction="""
    You are a conversation memory agent that maintains detailed memory of our interactions.
    
    Your capabilities include:
    1. Storing important conversation topics and details
    2. Recalling relevant information from previous conversations
    3. Summarizing conversation history
    4. Maintaining context across long interactions
    
    Current conversation memories: {conversation_memories}
    
    Always use your memory tools to:
    - Store important information the user shares
    - Recall relevant context when responding
    - Build on previous conversations
    - Provide personalized responses based on history
    """,
    tools=[store_conversation_memory, recall_conversation_memory, summarize_conversation_history]
)
