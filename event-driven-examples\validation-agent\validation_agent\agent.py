from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext
import re
from typing import Any, Dict


def validate_email(email: str, tool_context: ToolContext) -> dict:
    """Validate an email address format."""
    print(f"--- Tool: validate_email called for {email} ---")
    
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    is_valid = re.match(email_pattern, email) is not None
    
    return {
        "action": "validate_email",
        "email": email,
        "is_valid": is_valid,
        "message": "Valid email address" if is_valid else "Invalid email format",
        "status": "success"
    }


def validate_phone_number(phone: str, country_code: str, tool_context: ToolContext) -> dict:
    """Validate a phone number format."""
    print(f"--- Tool: validate_phone_number called for {phone} ---")
    
    # Remove common formatting characters
    clean_phone = re.sub(r'[^\d+]', '', phone)
    
    # Basic validation patterns by country
    patterns = {
        "US": r'^\+?1?[2-9]\d{2}[2-9]\d{2}\d{4}$',
        "UK": r'^\+?44[1-9]\d{8,9}$',
        "INTERNATIONAL": r'^\+?[1-9]\d{1,14}$'
    }
    
    pattern = patterns.get(country_code.upper(), patterns["INTERNATIONAL"])
    is_valid = re.match(pattern, clean_phone) is not None
    
    return {
        "action": "validate_phone_number",
        "phone": phone,
        "country_code": country_code,
        "is_valid": is_valid,
        "cleaned_phone": clean_phone,
        "message": "Valid phone number" if is_valid else f"Invalid phone format for {country_code}",
        "status": "success"
    }


def validate_data_structure(data: str, schema_type: str, tool_context: ToolContext) -> dict:
    """Validate data against a specified schema type."""
    print(f"--- Tool: validate_data_structure called for {schema_type} ---")
    
    try:
        import json
        parsed_data = json.loads(data)
        
        if schema_type == "user_profile":
            required_fields = ["name", "email", "age"]
            missing_fields = [field for field in required_fields if field not in parsed_data]
            
            if missing_fields:
                return {
                    "action": "validate_data_structure",
                    "is_valid": False,
                    "missing_fields": missing_fields,
                    "message": f"Missing required fields: {', '.join(missing_fields)}",
                    "status": "success"
                }
            
            # Additional field validation
            if not isinstance(parsed_data.get("age"), int) or parsed_data["age"] < 0:
                return {
                    "action": "validate_data_structure",
                    "is_valid": False,
                    "message": "Age must be a positive integer",
                    "status": "success"
                }
        
        return {
            "action": "validate_data_structure",
            "is_valid": True,
            "data": parsed_data,
            "message": f"Data structure is valid for {schema_type}",
            "status": "success"
        }
        
    except json.JSONDecodeError:
        return {
            "action": "validate_data_structure",
            "is_valid": False,
            "message": "Invalid JSON format",
            "status": "success"
        }


def sanitize_input(input_text: str, sanitization_type: str, tool_context: ToolContext) -> dict:
    """Sanitize input text based on the specified type."""
    print(f"--- Tool: sanitize_input called for {sanitization_type} ---")
    
    if sanitization_type == "html":
        # Remove HTML tags
        sanitized = re.sub(r'<[^>]+>', '', input_text)
        # Remove potentially dangerous characters
        sanitized = re.sub(r'[<>&"\']', '', sanitized)
    
    elif sanitization_type == "sql":
        # Basic SQL injection prevention
        dangerous_patterns = ['--', ';', 'DROP', 'DELETE', 'INSERT', 'UPDATE', 'EXEC']
        sanitized = input_text
        for pattern in dangerous_patterns:
            sanitized = sanitized.replace(pattern, '')
    
    elif sanitization_type == "filename":
        # Remove dangerous filename characters
        sanitized = re.sub(r'[<>:"/\\|?*]', '', input_text)
        sanitized = sanitized.strip('. ')
    
    else:
        # General sanitization
        sanitized = re.sub(r'[^\w\s.-]', '', input_text)
    
    return {
        "action": "sanitize_input",
        "original": input_text,
        "sanitized": sanitized,
        "sanitization_type": sanitization_type,
        "changes_made": input_text != sanitized,
        "status": "success"
    }


# Create the validation agent
root_agent = Agent(
    name="validation_agent",
    model="gemini-2.0-flash",
    description="Agent that provides robust input validation and error handling for production-ready applications",
    instruction="""
    You are a validation agent that specializes in input validation, data sanitization, and error handling.
    
    Your capabilities include:
    1. Email address validation
    2. Phone number validation (multiple country formats)
    3. Data structure validation against schemas
    4. Input sanitization for security
    5. Custom validation rules
    
    When users need validation:
    - Use validate_email for email address checking
    - Use validate_phone_number for phone number validation
    - Use validate_data_structure for complex data validation
    - Use sanitize_input for security sanitization
    
    Always provide clear, actionable feedback about validation results and suggest corrections when validation fails.
    """,
    tools=[validate_email, validate_phone_number, validate_data_structure, sanitize_input]
)
