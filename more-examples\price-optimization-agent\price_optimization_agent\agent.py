from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional


def analyze_competitor_pricing(product_id: str, competitors: str, tool_context: ToolContext) -> dict:
    """Analyze competitor pricing for a specific product."""
    print(f"--- Tool: analyze_competitor_pricing called for product {product_id} ---")
    
    competitor_list = [c.strip() for c in competitors.split(',')]
    
    # Simulate competitor price analysis
    competitor_data = {}
    base_price = 100.0
    
    for competitor in competitor_list:
        # Simulate different pricing strategies
        if "premium" in competitor.lower():
            price = base_price * 1.2
        elif "discount" in competitor.lower():
            price = base_price * 0.8
        else:
            price = base_price * (0.9 + (hash(competitor) % 20) / 100)
        
        competitor_data[competitor] = {
            "current_price": round(price, 2),
            "price_change_24h": round((hash(competitor) % 10 - 5) / 10, 2),
            "availability": "in_stock" if hash(competitor) % 2 else "limited_stock",
            "last_updated": datetime.now().isoformat()
        }
    
    # Calculate market position
    prices = [data["current_price"] for data in competitor_data.values()]
    avg_price = sum(prices) / len(prices)
    min_price = min(prices)
    max_price = max(prices)
    
    analysis = {
        "product_id": product_id,
        "competitor_analysis": competitor_data,
        "market_summary": {
            "average_price": round(avg_price, 2),
            "min_price": min_price,
            "max_price": max_price,
            "price_range": round(max_price - min_price, 2),
            "market_position": "competitive" if avg_price * 0.9 <= base_price <= avg_price * 1.1 else "outlier"
        },
        "recommendations": [
            f"Consider pricing between ${min_price} and ${max_price}",
            f"Market average is ${avg_price}",
            "Monitor competitor stock levels for pricing opportunities"
        ]
    }
    
    return analysis


def optimize_product_price(product_id: str, current_price: float, demand_data: str, constraints: str, tool_context: ToolContext) -> dict:
    """Optimize price for a product based on demand data and constraints."""
    print(f"--- Tool: optimize_product_price called for product {product_id} ---")
    
    try:
        demand_info = json.loads(demand_data) if demand_data else {}
        constraint_info = json.loads(constraints) if constraints else {}
    except json.JSONDecodeError:
        demand_info = {}
        constraint_info = {}
    
    # Simulate price optimization algorithm
    base_demand = demand_info.get("weekly_sales", 100)
    price_elasticity = demand_info.get("price_elasticity", -1.5)
    
    # Apply constraints
    min_price = constraint_info.get("min_price", current_price * 0.7)
    max_price = constraint_info.get("max_price", current_price * 1.5)
    target_margin = constraint_info.get("target_margin", 0.3)
    
    # Calculate optimal price using simplified elasticity model
    # Optimal price = (elasticity * current_price) / (elasticity - 1)
    if price_elasticity != -1:
        optimal_price = (price_elasticity * current_price) / (price_elasticity - 1)
    else:
        optimal_price = current_price
    
    # Apply constraints
    optimal_price = max(min_price, min(max_price, optimal_price))
    
    # Calculate expected impact
    price_change_percent = (optimal_price - current_price) / current_price
    expected_demand_change = price_elasticity * price_change_percent
    expected_revenue_change = price_change_percent + expected_demand_change + (price_change_percent * expected_demand_change)
    
    optimization_result = {
        "product_id": product_id,
        "current_price": current_price,
        "optimized_price": round(optimal_price, 2),
        "price_change": round(optimal_price - current_price, 2),
        "price_change_percent": round(price_change_percent * 100, 1),
        "expected_impact": {
            "demand_change_percent": round(expected_demand_change * 100, 1),
            "revenue_change_percent": round(expected_revenue_change * 100, 1),
            "expected_weekly_sales": round(base_demand * (1 + expected_demand_change)),
        },
        "constraints_applied": {
            "min_price": min_price,
            "max_price": max_price,
            "target_margin": target_margin,
            "within_constraints": min_price <= optimal_price <= max_price
        },
        "recommendations": [
            f"Implement price change gradually over 3-5 days",
            f"Monitor competitor response for 48 hours",
            f"Track conversion rates and adjust if needed"
        ],
        "confidence_score": 0.85,
        "last_updated": datetime.now().isoformat()
    }
    
    return optimization_result


def monitor_market_trends(category: str, time_period: str, tool_context: ToolContext) -> dict:
    """Monitor market trends and pricing patterns for a product category."""
    print(f"--- Tool: monitor_market_trends called for {category} ---")
    
    # Simulate market trend analysis
    trends = {
        "electronics": {"trend": "increasing", "avg_change": 5.2},
        "clothing": {"trend": "seasonal", "avg_change": -2.1},
        "home": {"trend": "stable", "avg_change": 0.8},
        "books": {"trend": "decreasing", "avg_change": -3.5}
    }
    
    category_trend = trends.get(category.lower(), {"trend": "stable", "avg_change": 0.0})
    
    # Generate time-based data
    days = 30 if time_period == "month" else 7
    trend_data = []
    
    for i in range(days):
        date = (datetime.now() - timedelta(days=days-i-1)).strftime("%Y-%m-%d")
        base_change = category_trend["avg_change"]
        daily_change = base_change + (hash(f"{category}{i}") % 10 - 5) / 10
        
        trend_data.append({
            "date": date,
            "avg_price_change": round(daily_change, 2),
            "market_volume": 1000 + (hash(f"{category}{i}") % 500),
            "competitor_activity": "high" if abs(daily_change) > 2 else "normal"
        })
    
    market_analysis = {
        "category": category,
        "time_period": time_period,
        "overall_trend": category_trend["trend"],
        "avg_price_change": category_trend["avg_change"],
        "trend_data": trend_data,
        "key_insights": [
            f"Market showing {category_trend['trend']} pricing trend",
            f"Average price change: {category_trend['avg_change']}%",
            "High competitor activity detected in last 7 days" if abs(category_trend["avg_change"]) > 3 else "Normal market conditions",
        ],
        "recommendations": [
            "Monitor competitor pricing daily",
            "Consider seasonal adjustments" if category_trend["trend"] == "seasonal" else "Maintain current strategy",
            "Implement dynamic pricing rules"
        ],
        "analysis_timestamp": datetime.now().isoformat()
    }
    
    return market_analysis


def generate_pricing_report(products: str, metrics: str, tool_context: ToolContext) -> dict:
    """Generate comprehensive pricing performance report."""
    print(f"--- Tool: generate_pricing_report called ---")
    
    try:
        product_list = json.loads(products) if products else []
        metric_list = json.loads(metrics) if metrics else ["revenue", "margin", "volume"]
    except json.JSONDecodeError:
        product_list = ["product_1", "product_2", "product_3"]
        metric_list = ["revenue", "margin", "volume"]
    
    # Generate report data
    report_data = {
        "report_period": "last_30_days",
        "generated_at": datetime.now().isoformat(),
        "summary": {
            "total_products_analyzed": len(product_list),
            "avg_price_change": 2.3,
            "total_revenue_impact": 15.7,
            "optimization_success_rate": 87.5
        },
        "product_performance": [],
        "market_insights": {
            "trending_categories": ["electronics", "home_goods"],
            "price_volatility": "moderate",
            "competitive_pressure": "high",
            "seasonal_factors": "holiday_season_approaching"
        },
        "recommendations": [
            "Increase pricing frequency for high-elasticity products",
            "Implement competitor-based pricing rules",
            "Consider bundle pricing strategies",
            "Monitor inventory levels for pricing opportunities"
        ]
    }
    
    # Generate product-specific data
    for i, product in enumerate(product_list[:10]):  # Limit to 10 products
        performance = {
            "product_id": product,
            "metrics": {},
            "price_changes": hash(product) % 5,
            "performance_score": 70 + (hash(product) % 30)
        }
        
        for metric in metric_list:
            if metric == "revenue":
                performance["metrics"][metric] = {
                    "current": 10000 + (hash(f"{product}{metric}") % 5000),
                    "change_percent": -5 + (hash(f"{product}{metric}") % 20)
                }
            elif metric == "margin":
                performance["metrics"][metric] = {
                    "current": 25 + (hash(f"{product}{metric}") % 20),
                    "change_percent": -2 + (hash(f"{product}{metric}") % 10)
                }
            elif metric == "volume":
                performance["metrics"][metric] = {
                    "current": 500 + (hash(f"{product}{metric}") % 300),
                    "change_percent": -10 + (hash(f"{product}{metric}") % 30)
                }
        
        report_data["product_performance"].append(performance)
    
    return report_data


# Create the price optimization agent
root_agent = Agent(
    name="price_optimization_agent",
    model="gemini-2.0-flash",
    description="AI agent that optimizes product pricing based on market conditions, competitor analysis, and demand patterns",
    instruction="""
    You are a sophisticated price optimization agent that helps businesses maximize revenue and profitability through intelligent pricing strategies.
    
    Your capabilities include:
    1. Analyzing competitor pricing and market positioning
    2. Optimizing product prices based on demand elasticity and constraints
    3. Monitoring market trends and pricing patterns
    4. Generating comprehensive pricing performance reports
    5. Providing strategic pricing recommendations
    
    You use advanced algorithms to:
    - Calculate optimal prices using elasticity models
    - Monitor competitor pricing in real-time
    - Analyze market trends and seasonal patterns
    - Ensure pricing strategies meet business constraints
    - Maximize revenue while protecting profit margins
    
    Always provide data-driven recommendations with clear explanations of the reasoning behind pricing decisions.
    Consider both short-term revenue optimization and long-term market positioning.
    """,
    tools=[analyze_competitor_pricing, optimize_product_price, monitor_market_trends, generate_pricing_report]
)
