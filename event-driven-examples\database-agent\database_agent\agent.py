from google.adk.agents import Agent
from google.adk.tools.tool_context import <PERSON>l<PERSON>ontex<PERSON>
from datetime import datetime
from typing import Dict, Any, List, Optional
import re
import json


def connect_database(db_type: str, connection_string: str, tool_context: ToolContext) -> dict:
    """Connect to a database and store connection information.
    
    Args:
        db_type: Type of database ('sqlite', 'postgresql', 'mysql')
        connection_string: Database connection string
        tool_context: Context for accessing session state
        
    Returns:
        A dictionary with connection status
    """
    print(f"--- Tool: connect_database called for {db_type} ---")
    
    # Validate database type
    supported_types = ['sqlite', 'postgresql', 'mysql']
    if db_type not in supported_types:
        return {
            "action": "connect_database",
            "error": f"Database type {db_type} not supported",
            "supported_types": supported_types,
            "status": "error"
        }
    
    # Simulate connection validation
    if not connection_string or len(connection_string) < 10:
        return {
            "action": "connect_database",
            "error": "Invalid connection string",
            "status": "error"
        }
    
    # Store connection info (in real implementation, this would be actual connection)
    connections = tool_context.state.get("database_connections", {})
    connection_id = f"conn_{len(connections) + 1}"
    
    connections[connection_id] = {
        "db_type": db_type,
        "connection_string": connection_string,
        "connected_at": datetime.now().isoformat(),
        "status": "connected",
        "queries_executed": 0
    }
    
    tool_context.state["database_connections"] = connections
    tool_context.state["active_connection"] = connection_id
    
    return {
        "action": "connect_database",
        "connection_id": connection_id,
        "db_type": db_type,
        "message": f"Successfully connected to {db_type} database",
        "status": "success"
    }


def execute_query(sql_query: str, parameters: Optional[List] = None, tool_context: ToolContext) -> dict:
    """Execute a SQL query on the active database connection.
    
    Args:
        sql_query: SQL query to execute
        parameters: Optional parameters for parameterized queries
        tool_context: Context for accessing session state
        
    Returns:
        A dictionary with query results
    """
    print(f"--- Tool: execute_query called with query: {sql_query[:100]}... ---")
    
    # Check for active connection
    active_connection = tool_context.state.get("active_connection")
    if not active_connection:
        return {
            "action": "execute_query",
            "error": "No active database connection",
            "status": "error"
        }
    
    connections = tool_context.state.get("database_connections", {})
    connection_info = connections.get(active_connection)
    
    if not connection_info:
        return {
            "action": "execute_query",
            "error": "Active connection not found",
            "status": "error"
        }
    
    # Validate SQL query for safety
    validation_result = validate_sql_query(sql_query)
    if not validation_result["valid"]:
        return {
            "action": "execute_query",
            "error": f"Query validation failed: {validation_result['error']}",
            "status": "error"
        }
    
    try:
        # Simulate query execution
        result = simulate_query_execution(sql_query, parameters, connection_info["db_type"])
        
        # Update connection statistics
        connection_info["queries_executed"] += 1
        connection_info["last_query_at"] = datetime.now().isoformat()
        connections[active_connection] = connection_info
        tool_context.state["database_connections"] = connections
        
        return {
            "action": "execute_query",
            "query": sql_query,
            "parameters": parameters,
            "result": result,
            "executed_at": datetime.now().isoformat(),
            "status": "success"
        }
        
    except Exception as e:
        return {
            "action": "execute_query",
            "error": f"Query execution error: {str(e)}",
            "status": "error"
        }


def validate_sql_query(sql_query: str) -> dict:
    """Validate SQL query for safety and correctness.
    
    Args:
        sql_query: SQL query to validate
        
    Returns:
        A dictionary with validation results
    """
    # Basic SQL injection prevention
    dangerous_patterns = [
        r';\s*drop\s+table',
        r';\s*delete\s+from',
        r';\s*truncate\s+table',
        r'union\s+select.*--',
        r'exec\s*\(',
        r'xp_cmdshell'
    ]
    
    query_lower = sql_query.lower()
    
    for pattern in dangerous_patterns:
        if re.search(pattern, query_lower):
            return {
                "valid": False,
                "error": f"Potentially dangerous SQL pattern detected: {pattern}"
            }
    
    # Check for basic SQL structure
    sql_keywords = ['select', 'insert', 'update', 'delete', 'create', 'alter', 'drop']
    has_sql_keyword = any(keyword in query_lower for keyword in sql_keywords)
    
    if not has_sql_keyword:
        return {
            "valid": False,
            "error": "Query does not contain valid SQL keywords"
        }
    
    return {
        "valid": True,
        "message": "Query validation passed"
    }


def simulate_query_execution(sql_query: str, parameters: Optional[List], db_type: str) -> dict:
    """Simulate SQL query execution and return mock results.
    
    Args:
        sql_query: SQL query to simulate
        parameters: Query parameters
        db_type: Database type
        
    Returns:
        A dictionary with simulated results
    """
    query_lower = sql_query.lower().strip()
    
    if query_lower.startswith('select'):
        return simulate_select_query(sql_query, db_type)
    elif query_lower.startswith('insert'):
        return simulate_insert_query(sql_query, db_type)
    elif query_lower.startswith('update'):
        return simulate_update_query(sql_query, db_type)
    elif query_lower.startswith('delete'):
        return simulate_delete_query(sql_query, db_type)
    elif query_lower.startswith('create'):
        return simulate_create_query(sql_query, db_type)
    else:
        return {
            "query_type": "other",
            "message": f"Query executed successfully on {db_type}",
            "affected_rows": 0
        }


def simulate_select_query(sql_query: str, db_type: str) -> dict:
    """Simulate SELECT query results."""
    # Extract table name (basic parsing)
    table_match = re.search(r'from\s+(\w+)', sql_query.lower())
    table_name = table_match.group(1) if table_match else "unknown_table"
    
    # Generate mock data based on common table names
    if 'user' in table_name:
        mock_data = [
            {"id": 1, "name": "John Doe", "email": "<EMAIL>", "created_at": "2024-01-01"},
            {"id": 2, "name": "Jane Smith", "email": "<EMAIL>", "created_at": "2024-01-02"},
            {"id": 3, "name": "Bob Johnson", "email": "<EMAIL>", "created_at": "2024-01-03"}
        ]
    elif 'product' in table_name:
        mock_data = [
            {"id": 1, "name": "Laptop", "price": 999.99, "category": "Electronics"},
            {"id": 2, "name": "Book", "price": 19.99, "category": "Education"},
            {"id": 3, "name": "Coffee Mug", "price": 12.99, "category": "Kitchen"}
        ]
    else:
        mock_data = [
            {"id": 1, "column1": "value1", "column2": "value2"},
            {"id": 2, "column1": "value3", "column2": "value4"}
        ]
    
    return {
        "query_type": "select",
        "table_name": table_name,
        "rows_returned": len(mock_data),
        "data": mock_data,
        "columns": list(mock_data[0].keys()) if mock_data else []
    }


def simulate_insert_query(sql_query: str, db_type: str) -> dict:
    """Simulate INSERT query results."""
    return {
        "query_type": "insert",
        "affected_rows": 1,
        "last_insert_id": 123,
        "message": "Record inserted successfully"
    }


def simulate_update_query(sql_query: str, db_type: str) -> dict:
    """Simulate UPDATE query results."""
    return {
        "query_type": "update",
        "affected_rows": 2,
        "message": "Records updated successfully"
    }


def simulate_delete_query(sql_query: str, db_type: str) -> dict:
    """Simulate DELETE query results."""
    return {
        "query_type": "delete",
        "affected_rows": 1,
        "message": "Records deleted successfully"
    }


def simulate_create_query(sql_query: str, db_type: str) -> dict:
    """Simulate CREATE query results."""
    return {
        "query_type": "create",
        "message": "Table/schema created successfully"
    }


def create_table(table_name: str, columns: Dict[str, str], tool_context: ToolContext) -> dict:
    """Create a new table with specified columns.
    
    Args:
        table_name: Name of the table to create
        columns: Dictionary of column names and their data types
        tool_context: Context for accessing session state
        
    Returns:
        A dictionary with creation results
    """
    print(f"--- Tool: create_table called for {table_name} ---")
    
    # Check for active connection
    active_connection = tool_context.state.get("active_connection")
    if not active_connection:
        return {
            "action": "create_table",
            "error": "No active database connection",
            "status": "error"
        }
    
    # Validate table name
    if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', table_name):
        return {
            "action": "create_table",
            "error": "Invalid table name",
            "status": "error"
        }
    
    # Build CREATE TABLE SQL
    column_definitions = []
    for col_name, col_type in columns.items():
        if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', col_name):
            return {
                "action": "create_table",
                "error": f"Invalid column name: {col_name}",
                "status": "error"
            }
        column_definitions.append(f"{col_name} {col_type}")
    
    sql_query = f"CREATE TABLE {table_name} ({', '.join(column_definitions)})"
    
    # Execute the query
    result = execute_query(sql_query, None, tool_context)
    
    if result["status"] == "success":
        # Store table schema
        schemas = tool_context.state.get("table_schemas", {})
        schemas[table_name] = {
            "columns": columns,
            "created_at": datetime.now().isoformat()
        }
        tool_context.state["table_schemas"] = schemas
    
    return {
        "action": "create_table",
        "table_name": table_name,
        "columns": columns,
        "sql_query": sql_query,
        "result": result,
        "status": result["status"]
    }


def list_connections(tool_context: ToolContext) -> dict:
    """List all database connections.
    
    Args:
        tool_context: Context for accessing session state
        
    Returns:
        A dictionary with all connections
    """
    print("--- Tool: list_connections called ---")
    
    connections = tool_context.state.get("database_connections", {})
    active_connection = tool_context.state.get("active_connection")
    
    # Remove sensitive information
    safe_connections = {}
    for conn_id, conn_info in connections.items():
        safe_connections[conn_id] = {
            "db_type": conn_info["db_type"],
            "connected_at": conn_info["connected_at"],
            "status": conn_info["status"],
            "queries_executed": conn_info["queries_executed"],
            "is_active": conn_id == active_connection
        }
    
    return {
        "action": "list_connections",
        "connections": safe_connections,
        "active_connection": active_connection,
        "total_connections": len(connections)
    }


# Create the database agent
root_agent = Agent(
    name="database_agent",
    model="gemini-2.0-flash",
    description="Agent that integrates with databases for persistent data storage and complex queries",
    instruction="""
    You are a database agent that specializes in database operations and data management.
    
    Your capabilities include:
    1. Database connection management (SQLite, PostgreSQL, MySQL)
    2. SQL query execution with safety validation
    3. Table creation and schema management
    4. Data CRUD operations (Create, Read, Update, Delete)
    5. Query optimization and performance analysis
    6. Transaction management and data consistency
    
    Supported database types:
    - SQLite (file-based, good for development)
    - PostgreSQL (full-featured relational database)
    - MySQL (popular web application database)
    
    When users want to work with databases:
    - Use connect_database to establish database connections
    - Use execute_query for running SQL queries safely
    - Use create_table for creating new tables with proper schemas
    - Use list_connections to view all database connections
    
    Always emphasize security and best practices:
    - Validate all SQL queries for injection attacks
    - Use parameterized queries when possible
    - Implement proper error handling
    - Provide clear feedback on query results
    - Suggest query optimizations when appropriate
    
    For demonstrations, you can simulate database operations with realistic mock data.
    Remember to explain SQL concepts and best practices to help users learn.
    """,
    tools=[connect_database, execute_query, create_table, list_connections]
)
