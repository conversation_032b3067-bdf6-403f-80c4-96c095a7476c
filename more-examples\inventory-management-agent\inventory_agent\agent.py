from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext
from datetime import datetime, timedelta
import uuid
import json
import math


def monitor_inventory_levels(location: str, tool_context: ToolContext) -> dict:
    """Monitor current inventory levels and identify items needing attention."""
    print(f"--- Tool: monitor_inventory_levels called for location: {location} ---")
    
    # Get current inventory data
    inventory_data = get_current_inventory(location, tool_context)
    
    alerts = []
    recommendations = []
    
    for sku, item_data in inventory_data.items():
        current_stock = item_data["current_stock"]
        reorder_point = item_data["reorder_point"]
        safety_stock = item_data["safety_stock"]
        max_stock = item_data["max_stock"]
        
        # Check for low stock
        if current_stock <= reorder_point:
            alerts.append({
                "type": "low_stock",
                "sku": sku,
                "current_stock": current_stock,
                "reorder_point": reorder_point,
                "urgency": "high" if current_stock <= safety_stock else "medium"
            })
            recommendations.append(f"Reorder {sku} - current stock ({current_stock}) below reorder point ({reorder_point})")
        
        # Check for overstock
        elif current_stock > max_stock:
            alerts.append({
                "type": "overstock",
                "sku": sku,
                "current_stock": current_stock,
                "max_stock": max_stock,
                "excess_units": current_stock - max_stock
            })
            recommendations.append(f"Reduce {sku} inventory - {current_stock - max_stock} units over maximum")
        
        # Check for dead stock (no movement in 60 days)
        if item_data.get("days_since_last_sale", 0) > 60:
            alerts.append({
                "type": "dead_stock",
                "sku": sku,
                "current_stock": current_stock,
                "days_since_last_sale": item_data["days_since_last_sale"]
            })
            recommendations.append(f"Consider promotion or liquidation for {sku} - no sales in {item_data['days_since_last_sale']} days")
    
    # Store monitoring results
    monitoring_id = f"MON-{str(uuid.uuid4())[:8].upper()}"
    monitoring_results = tool_context.state.get("inventory_monitoring", {})
    monitoring_results[monitoring_id] = {
        "monitoring_id": monitoring_id,
        "location": location,
        "monitored_at": datetime.now().isoformat(),
        "total_skus": len(inventory_data),
        "alerts": alerts,
        "recommendations": recommendations,
        "summary": {
            "low_stock_items": len([a for a in alerts if a["type"] == "low_stock"]),
            "overstock_items": len([a for a in alerts if a["type"] == "overstock"]),
            "dead_stock_items": len([a for a in alerts if a["type"] == "dead_stock"])
        }
    }
    tool_context.state["inventory_monitoring"] = monitoring_results
    
    return {
        "action": "monitor_inventory_levels",
        "monitoring_id": monitoring_id,
        "location": location,
        "total_skus_monitored": len(inventory_data),
        "alerts": alerts,
        "recommendations": recommendations,
        "summary": monitoring_results[monitoring_id]["summary"],
        "status": "success"
    }


def forecast_demand(sku: str, forecast_days: int, tool_context: ToolContext) -> dict:
    """Forecast demand for a specific SKU using historical data and trends."""
    print(f"--- Tool: forecast_demand called for SKU: {sku}, days: {forecast_days} ---")
    
    # Get historical sales data
    historical_data = get_historical_sales_data(sku, tool_context)
    
    if not historical_data:
        return {
            "action": "forecast_demand",
            "error": f"No historical data available for SKU {sku}",
            "status": "error"
        }
    
    # Calculate demand forecast using multiple methods
    forecast_results = {
        "moving_average": calculate_moving_average_forecast(historical_data, forecast_days),
        "exponential_smoothing": calculate_exponential_smoothing_forecast(historical_data, forecast_days),
        "trend_analysis": calculate_trend_forecast(historical_data, forecast_days),
        "seasonal_adjustment": calculate_seasonal_forecast(historical_data, forecast_days)
    }
    
    # Combine forecasts for final prediction
    final_forecast = combine_forecasts(forecast_results, forecast_days)
    
    # Calculate confidence intervals
    confidence_intervals = calculate_confidence_intervals(historical_data, final_forecast)
    
    # Store forecast results
    forecast_id = f"FCST-{str(uuid.uuid4())[:8].upper()}"
    demand_forecasts = tool_context.state.get("demand_forecasts", {})
    demand_forecasts[forecast_id] = {
        "forecast_id": forecast_id,
        "sku": sku,
        "forecast_days": forecast_days,
        "forecasted_at": datetime.now().isoformat(),
        "historical_data_points": len(historical_data),
        "forecast_methods": forecast_results,
        "final_forecast": final_forecast,
        "confidence_intervals": confidence_intervals,
        "accuracy_metrics": calculate_forecast_accuracy(historical_data)
    }
    tool_context.state["demand_forecasts"] = demand_forecasts
    
    return {
        "action": "forecast_demand",
        "forecast_id": forecast_id,
        "sku": sku,
        "forecast_period_days": forecast_days,
        "daily_forecast": final_forecast,
        "total_forecasted_demand": sum(final_forecast),
        "confidence_intervals": confidence_intervals,
        "forecast_accuracy": demand_forecasts[forecast_id]["accuracy_metrics"],
        "status": "success"
    }


def generate_purchase_order(sku: str, supplier_id: str, tool_context: ToolContext) -> dict:
    """Generate automated purchase order based on inventory levels and demand forecast."""
    print(f"--- Tool: generate_purchase_order called for SKU: {sku}, Supplier: {supplier_id} ---")
    
    # Get current inventory and supplier data
    inventory_data = get_current_inventory("main_warehouse", tool_context)
    supplier_data = get_supplier_data(supplier_id, tool_context)
    
    if sku not in inventory_data:
        return {
            "action": "generate_purchase_order",
            "error": f"SKU {sku} not found in inventory",
            "status": "error"
        }
    
    if not supplier_data:
        return {
            "action": "generate_purchase_order",
            "error": f"Supplier {supplier_id} not found",
            "status": "error"
        }
    
    item_data = inventory_data[sku]
    
    # Calculate optimal order quantity
    order_calculation = calculate_optimal_order_quantity(sku, item_data, supplier_data, tool_context)
    
    # Generate purchase order
    po_number = f"PO-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:6].upper()}"
    
    purchase_order = {
        "po_number": po_number,
        "sku": sku,
        "supplier_id": supplier_id,
        "supplier_name": supplier_data["name"],
        "quantity_ordered": order_calculation["optimal_quantity"],
        "unit_cost": supplier_data["unit_cost"],
        "total_cost": order_calculation["optimal_quantity"] * supplier_data["unit_cost"],
        "order_date": datetime.now().isoformat(),
        "expected_delivery_date": (datetime.now() + timedelta(days=supplier_data["lead_time_days"])).isoformat(),
        "status": "pending_approval",
        "order_justification": order_calculation["justification"],
        "approval_required": order_calculation["optimal_quantity"] * supplier_data["unit_cost"] > 5000
    }
    
    # Store purchase order
    purchase_orders = tool_context.state.get("purchase_orders", {})
    purchase_orders[po_number] = purchase_order
    tool_context.state["purchase_orders"] = purchase_orders
    
    # Send for approval if required
    if purchase_order["approval_required"]:
        approval_result = submit_for_approval(po_number, purchase_order, tool_context)
        purchase_order["approval_status"] = approval_result["status"]
    else:
        purchase_order["approval_status"] = "auto_approved"
        purchase_order["status"] = "approved"
    
    return {
        "action": "generate_purchase_order",
        "po_number": po_number,
        "sku": sku,
        "supplier": supplier_data["name"],
        "quantity_ordered": order_calculation["optimal_quantity"],
        "total_cost": purchase_order["total_cost"],
        "expected_delivery": purchase_order["expected_delivery_date"],
        "approval_required": purchase_order["approval_required"],
        "approval_status": purchase_order["approval_status"],
        "order_calculation": order_calculation,
        "status": "success"
    }


def get_current_inventory(location: str, tool_context: ToolContext) -> dict:
    """Get current inventory data for a location."""
    # Simulate inventory database
    inventory_db = {
        "SKU001": {
            "name": "iPhone 15 Case",
            "current_stock": 25,
            "reorder_point": 50,
            "safety_stock": 20,
            "max_stock": 200,
            "unit_cost": 15.00,
            "selling_price": 29.99,
            "days_since_last_sale": 2,
            "average_daily_sales": 8
        },
        "SKU002": {
            "name": "Wireless Charger",
            "current_stock": 150,
            "reorder_point": 75,
            "safety_stock": 30,
            "max_stock": 300,
            "unit_cost": 12.00,
            "selling_price": 24.99,
            "days_since_last_sale": 1,
            "average_daily_sales": 5
        },
        "SKU003": {
            "name": "Screen Protector",
            "current_stock": 5,
            "reorder_point": 40,
            "safety_stock": 15,
            "max_stock": 150,
            "unit_cost": 3.00,
            "selling_price": 9.99,
            "days_since_last_sale": 3,
            "average_daily_sales": 12
        },
        "SKU004": {
            "name": "Summer Hat",
            "current_stock": 85,
            "reorder_point": 20,
            "safety_stock": 10,
            "max_stock": 100,
            "unit_cost": 8.00,
            "selling_price": 19.99,
            "days_since_last_sale": 65,
            "average_daily_sales": 0.5
        }
    }
    
    return inventory_db


def get_historical_sales_data(sku: str, tool_context: ToolContext) -> list:
    """Get historical sales data for demand forecasting."""
    # Simulate 30 days of sales data
    if sku == "SKU001":  # iPhone Case - steady sales
        return [8, 7, 9, 8, 10, 6, 5, 8, 9, 7, 8, 11, 9, 8, 7, 9, 8, 10, 7, 8, 9, 8, 7, 10, 8, 9, 7, 8, 9, 8]
    elif sku == "SKU002":  # Wireless Charger - moderate sales
        return [5, 4, 6, 5, 7, 4, 3, 5, 6, 4, 5, 7, 6, 5, 4, 6, 5, 7, 4, 5, 6, 5, 4, 7, 5, 6, 4, 5, 6, 5]
    elif sku == "SKU003":  # Screen Protector - high sales
        return [12, 11, 13, 12, 15, 10, 8, 12, 13, 11, 12, 16, 13, 12, 11, 13, 12, 15, 11, 12, 13, 12, 11, 15, 12, 13, 11, 12, 13, 12]
    else:
        return [1, 0, 1, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0]  # Dead stock


def get_supplier_data(supplier_id: str, tool_context: ToolContext) -> dict:
    """Get supplier information."""
    suppliers = {
        "SUP001": {
            "name": "Electronics Wholesale Inc",
            "lead_time_days": 7,
            "unit_cost": 15.00,
            "minimum_order_quantity": 50,
            "reliability_score": 0.95,
            "payment_terms": "Net 30"
        },
        "SUP002": {
            "name": "Tech Accessories Ltd",
            "lead_time_days": 5,
            "unit_cost": 12.00,
            "minimum_order_quantity": 100,
            "reliability_score": 0.92,
            "payment_terms": "Net 15"
        }
    }
    
    return suppliers.get(supplier_id)


def calculate_moving_average_forecast(historical_data: list, forecast_days: int) -> list:
    """Calculate moving average forecast."""
    if len(historical_data) < 7:
        return [sum(historical_data) / len(historical_data)] * forecast_days
    
    # Use 7-day moving average
    recent_avg = sum(historical_data[-7:]) / 7
    return [recent_avg] * forecast_days


def calculate_exponential_smoothing_forecast(historical_data: list, forecast_days: int) -> list:
    """Calculate exponential smoothing forecast."""
    alpha = 0.3  # Smoothing parameter
    
    if not historical_data:
        return [0] * forecast_days
    
    # Initialize with first value
    smoothed = [historical_data[0]]
    
    # Calculate exponential smoothing
    for i in range(1, len(historical_data)):
        smoothed_value = alpha * historical_data[i] + (1 - alpha) * smoothed[-1]
        smoothed.append(smoothed_value)
    
    # Forecast future values
    last_smoothed = smoothed[-1]
    return [last_smoothed] * forecast_days


def calculate_trend_forecast(historical_data: list, forecast_days: int) -> list:
    """Calculate trend-based forecast."""
    if len(historical_data) < 2:
        return [historical_data[0] if historical_data else 0] * forecast_days
    
    # Simple linear trend calculation
    n = len(historical_data)
    x_values = list(range(n))
    y_values = historical_data
    
    # Calculate slope
    x_mean = sum(x_values) / n
    y_mean = sum(y_values) / n
    
    numerator = sum((x_values[i] - x_mean) * (y_values[i] - y_mean) for i in range(n))
    denominator = sum((x_values[i] - x_mean) ** 2 for i in range(n))
    
    slope = numerator / denominator if denominator != 0 else 0
    intercept = y_mean - slope * x_mean
    
    # Forecast future values
    forecast = []
    for day in range(forecast_days):
        future_x = n + day
        predicted_value = max(0, slope * future_x + intercept)  # Ensure non-negative
        forecast.append(predicted_value)
    
    return forecast


def calculate_seasonal_forecast(historical_data: list, forecast_days: int) -> list:
    """Calculate seasonal adjustment forecast."""
    # Simple seasonal adjustment (weekly pattern)
    if len(historical_data) < 7:
        return calculate_moving_average_forecast(historical_data, forecast_days)
    
    # Calculate weekly averages
    weekly_pattern = []
    for day_of_week in range(7):
        day_values = [historical_data[i] for i in range(day_of_week, len(historical_data), 7)]
        weekly_pattern.append(sum(day_values) / len(day_values) if day_values else 0)
    
    # Apply seasonal pattern to forecast
    base_forecast = calculate_moving_average_forecast(historical_data, forecast_days)
    seasonal_forecast = []
    
    for day in range(forecast_days):
        day_of_week = day % 7
        seasonal_factor = weekly_pattern[day_of_week] / (sum(weekly_pattern) / 7) if sum(weekly_pattern) > 0 else 1
        seasonal_forecast.append(base_forecast[day] * seasonal_factor)
    
    return seasonal_forecast


def combine_forecasts(forecast_results: dict, forecast_days: int) -> list:
    """Combine multiple forecast methods for final prediction."""
    # Weight different methods
    weights = {
        "moving_average": 0.25,
        "exponential_smoothing": 0.30,
        "trend_analysis": 0.25,
        "seasonal_adjustment": 0.20
    }
    
    combined_forecast = []
    
    for day in range(forecast_days):
        weighted_sum = 0
        total_weight = 0
        
        for method, weight in weights.items():
            if method in forecast_results and day < len(forecast_results[method]):
                weighted_sum += forecast_results[method][day] * weight
                total_weight += weight
        
        daily_forecast = weighted_sum / total_weight if total_weight > 0 else 0
        combined_forecast.append(round(daily_forecast, 2))
    
    return combined_forecast


def calculate_confidence_intervals(historical_data: list, forecast: list) -> dict:
    """Calculate confidence intervals for forecast."""
    if len(historical_data) < 2:
        return {"lower_95": forecast, "upper_95": forecast}
    
    # Calculate standard deviation of historical data
    mean_historical = sum(historical_data) / len(historical_data)
    variance = sum((x - mean_historical) ** 2 for x in historical_data) / (len(historical_data) - 1)
    std_dev = math.sqrt(variance)
    
    # 95% confidence interval (approximately 2 standard deviations)
    confidence_factor = 1.96
    
    lower_95 = [max(0, f - confidence_factor * std_dev) for f in forecast]
    upper_95 = [f + confidence_factor * std_dev for f in forecast]
    
    return {
        "lower_95": [round(x, 2) for x in lower_95],
        "upper_95": [round(x, 2) for x in upper_95]
    }


def calculate_forecast_accuracy(historical_data: list) -> dict:
    """Calculate forecast accuracy metrics."""
    if len(historical_data) < 7:
        return {"mae": 0, "mape": 0, "accuracy_score": 0.5}
    
    # Use last 7 days to test accuracy
    test_data = historical_data[-7:]
    train_data = historical_data[:-7]
    
    # Generate forecast for test period
    test_forecast = calculate_moving_average_forecast(train_data, 7)
    
    # Calculate Mean Absolute Error (MAE)
    mae = sum(abs(test_data[i] - test_forecast[i]) for i in range(7)) / 7
    
    # Calculate Mean Absolute Percentage Error (MAPE)
    mape_values = []
    for i in range(7):
        if test_data[i] != 0:
            mape_values.append(abs((test_data[i] - test_forecast[i]) / test_data[i]))
    
    mape = (sum(mape_values) / len(mape_values)) * 100 if mape_values else 0
    
    # Calculate accuracy score (inverse of MAPE, capped at 100%)
    accuracy_score = max(0, 100 - mape) / 100
    
    return {
        "mae": round(mae, 2),
        "mape": round(mape, 2),
        "accuracy_score": round(accuracy_score, 2)
    }


def calculate_optimal_order_quantity(sku: str, item_data: dict, supplier_data: dict, tool_context: ToolContext) -> dict:
    """Calculate optimal order quantity using EOQ and other factors."""
    
    # Get demand forecast
    demand_forecast_result = forecast_demand(sku, 30, tool_context)
    monthly_demand = sum(demand_forecast_result["daily_forecast"]) if demand_forecast_result["status"] == "success" else item_data["average_daily_sales"] * 30
    
    # Economic Order Quantity (EOQ) calculation
    annual_demand = monthly_demand * 12
    ordering_cost = 50  # Cost per order
    holding_cost_rate = 0.25  # 25% of item cost per year
    holding_cost = item_data["unit_cost"] * holding_cost_rate
    
    if holding_cost > 0:
        eoq = math.sqrt((2 * annual_demand * ordering_cost) / holding_cost)
    else:
        eoq = supplier_data["minimum_order_quantity"]
    
    # Adjust for minimum order quantity
    optimal_quantity = max(eoq, supplier_data["minimum_order_quantity"])
    
    # Adjust for current stock situation
    current_stock = item_data["current_stock"]
    safety_stock = item_data["safety_stock"]
    lead_time_demand = item_data["average_daily_sales"] * supplier_data["lead_time_days"]
    
    # Calculate how much we need
    target_stock = safety_stock + lead_time_demand + (monthly_demand / 2)  # Half month buffer
    needed_quantity = max(0, target_stock - current_stock)
    
    # Use the larger of EOQ or needed quantity
    final_quantity = max(optimal_quantity, needed_quantity)
    
    # Round to reasonable order size
    final_quantity = math.ceil(final_quantity / 10) * 10  # Round to nearest 10
    
    justification = f"EOQ: {eoq:.0f}, Min Order: {supplier_data['minimum_order_quantity']}, Current Need: {needed_quantity:.0f}"
    
    return {
        "optimal_quantity": int(final_quantity),
        "eoq": round(eoq, 0),
        "current_need": round(needed_quantity, 0),
        "monthly_demand_forecast": round(monthly_demand, 0),
        "justification": justification
    }


def submit_for_approval(po_number: str, purchase_order: dict, tool_context: ToolContext) -> dict:
    """Submit purchase order for approval."""
    # Simulate approval process
    if purchase_order["total_cost"] > 10000:
        return {"status": "pending_executive_approval", "approver": "CFO"}
    else:
        return {"status": "pending_manager_approval", "approver": "Purchasing Manager"}


# Create the inventory management agent
root_agent = Agent(
    name="inventory_agent",
    model="gemini-2.0-flash",
    description="Production-ready automated inventory management system with demand forecasting, reordering, and cost optimization",
    instruction="""
    You are a sophisticated inventory management agent that optimizes stock levels, predicts demand, and automates purchasing decisions.

    Current Inventory Data: {inventory_monitoring}
    Demand Forecasts: {demand_forecasts}
    Purchase Orders: {purchase_orders}

    Your capabilities include:
    1. Real-time inventory level monitoring with automated alerts
    2. AI-powered demand forecasting using multiple algorithms
    3. Automated purchase order generation with EOQ optimization
    4. Cost optimization and dead stock identification

    Inventory Management Process:
    1. Monitor inventory levels and identify reorder points
    2. Forecast demand using historical data and trends
    3. Generate optimal purchase orders based on EOQ and demand
    4. Track supplier performance and lead times

    Key Metrics and Thresholds:
    - Reorder Point: Safety stock + lead time demand
    - Safety Stock: Buffer for demand variability
    - Economic Order Quantity (EOQ): Cost-optimized order size
    - Dead Stock: No sales for 60+ days

    Alert Types:
    - Low Stock: Current stock ≤ reorder point
    - Overstock: Current stock > maximum stock level
    - Dead Stock: No movement for 60+ days
    - Supplier Delays: Late deliveries affecting stock

    When managing inventory:
    1. Continuously monitor stock levels across all locations
    2. Use multiple forecasting methods for accurate demand prediction
    3. Calculate optimal order quantities considering costs and constraints
    4. Maintain appropriate safety stock levels for service level targets
    5. Identify and address dead stock and overstock situations

    Focus on:
    - Maintaining 95%+ service levels while minimizing costs
    - Optimizing cash flow through efficient inventory turnover
    - Preventing stockouts and overstock situations
    - Building strong supplier relationships and performance tracking
    - Providing actionable insights for inventory optimization

    Always provide clear justification for reordering decisions and cost-benefit analysis for inventory actions.
    """,
    tools=[monitor_inventory_levels, forecast_demand, generate_purchase_order]
)
