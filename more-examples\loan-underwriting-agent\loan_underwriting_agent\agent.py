from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import random


def analyze_credit_profile(applicant_id: str, credit_data: str, tool_context: ToolContext) -> dict:
    """Analyze applicant's credit profile and generate risk assessment."""
    print(f"--- Tool: analyze_credit_profile called for applicant {applicant_id} ---")
    
    try:
        credit_info = json.loads(credit_data) if credit_data else {}
    except json.JSONDecodeError:
        credit_info = {}
    
    # Simulate credit analysis
    credit_score = credit_info.get("credit_score", 650 + random.randint(-100, 150))
    payment_history = credit_info.get("payment_history", random.choice(["excellent", "good", "fair", "poor"]))
    credit_utilization = credit_info.get("credit_utilization", random.randint(10, 90))
    credit_age = credit_info.get("credit_age_months", random.randint(12, 240))
    
    # Calculate risk factors
    score_risk = "low" if credit_score >= 720 else "medium" if credit_score >= 650 else "high"
    utilization_risk = "low" if credit_utilization <= 30 else "medium" if credit_utilization <= 60 else "high"
    history_risk = {"excellent": "low", "good": "low", "fair": "medium", "poor": "high"}[payment_history]
    
    # Generate credit analysis
    analysis = {
        "applicant_id": applicant_id,
        "credit_summary": {
            "credit_score": credit_score,
            "score_range": "excellent" if credit_score >= 750 else "good" if credit_score >= 700 else "fair" if credit_score >= 650 else "poor",
            "payment_history": payment_history,
            "credit_utilization": f"{credit_utilization}%",
            "credit_age_years": round(credit_age / 12, 1),
            "total_accounts": random.randint(5, 25),
            "recent_inquiries": random.randint(0, 8)
        },
        "risk_assessment": {
            "overall_risk": max([score_risk, utilization_risk, history_risk], key=lambda x: {"low": 1, "medium": 2, "high": 3}[x]),
            "credit_score_risk": score_risk,
            "utilization_risk": utilization_risk,
            "payment_history_risk": history_risk,
            "risk_score": random.randint(1, 100)
        },
        "red_flags": [],
        "positive_factors": [],
        "recommendations": []
    }
    
    # Add red flags
    if credit_score < 600:
        analysis["red_flags"].append("Very low credit score")
    if credit_utilization > 80:
        analysis["red_flags"].append("High credit utilization")
    if payment_history == "poor":
        analysis["red_flags"].append("Poor payment history")
    
    # Add positive factors
    if credit_score > 750:
        analysis["positive_factors"].append("Excellent credit score")
    if credit_utilization < 20:
        analysis["positive_factors"].append("Low credit utilization")
    if credit_age > 120:
        analysis["positive_factors"].append("Long credit history")
    
    # Add recommendations
    if analysis["risk_assessment"]["overall_risk"] == "high":
        analysis["recommendations"].append("Require additional documentation")
        analysis["recommendations"].append("Consider higher interest rate")
    elif analysis["risk_assessment"]["overall_risk"] == "medium":
        analysis["recommendations"].append("Standard approval process")
        analysis["recommendations"].append("Monitor account closely")
    else:
        analysis["recommendations"].append("Fast-track approval eligible")
        analysis["recommendations"].append("Offer premium rates")
    
    return analysis


def verify_income_employment(applicant_id: str, income_data: str, employment_data: str, tool_context: ToolContext) -> dict:
    """Verify applicant's income and employment information."""
    print(f"--- Tool: verify_income_employment called for applicant {applicant_id} ---")
    
    try:
        income_info = json.loads(income_data) if income_data else {}
        employment_info = json.loads(employment_data) if employment_data else {}
    except json.JSONDecodeError:
        income_info = {}
        employment_info = {}
    
    # Simulate income verification
    stated_income = income_info.get("annual_income", random.randint(30000, 150000))
    verified_income = stated_income * random.uniform(0.8, 1.1)  # Some variance
    employment_years = employment_info.get("years_employed", random.uniform(0.5, 15))
    job_title = employment_info.get("job_title", "Software Engineer")
    employer = employment_info.get("employer", "Tech Company Inc.")
    
    # Calculate verification confidence
    income_variance = abs(stated_income - verified_income) / stated_income
    verification_confidence = max(0.5, 1.0 - (income_variance * 2))
    
    verification_result = {
        "applicant_id": applicant_id,
        "income_verification": {
            "stated_annual_income": stated_income,
            "verified_annual_income": round(verified_income),
            "monthly_income": round(verified_income / 12),
            "income_variance": round(income_variance * 100, 1),
            "verification_status": "verified" if income_variance < 0.1 else "discrepancy_noted",
            "verification_confidence": round(verification_confidence * 100, 1),
            "income_sources": ["primary_employment", "bonus", "investment"] if verified_income > 80000 else ["primary_employment"]
        },
        "employment_verification": {
            "employer": employer,
            "job_title": job_title,
            "employment_status": "full_time",
            "years_employed": round(employment_years, 1),
            "employment_stability": "stable" if employment_years > 2 else "moderate" if employment_years > 1 else "new",
            "verification_method": "payroll_provider",
            "verification_date": datetime.now().isoformat()
        },
        "risk_factors": [],
        "stability_score": min(100, max(50, int(employment_years * 20 + verification_confidence * 30)))
    }
    
    # Add risk factors
    if income_variance > 0.2:
        verification_result["risk_factors"].append("Significant income discrepancy")
    if employment_years < 1:
        verification_result["risk_factors"].append("Short employment history")
    if verification_confidence < 0.7:
        verification_result["risk_factors"].append("Low verification confidence")
    
    return verification_result


def calculate_debt_to_income(applicant_id: str, monthly_income: float, existing_debts: str, tool_context: ToolContext) -> dict:
    """Calculate debt-to-income ratio and assess debt burden."""
    print(f"--- Tool: calculate_debt_to_income called for applicant {applicant_id} ---")
    
    try:
        debts = json.loads(existing_debts) if existing_debts else []
    except json.JSONDecodeError:
        debts = []
    
    # Calculate total monthly debt payments
    total_monthly_debt = 0
    debt_breakdown = {}
    
    if not debts:
        # Simulate some debts
        debts = [
            {"type": "credit_card", "balance": random.randint(1000, 15000), "min_payment": random.randint(50, 300)},
            {"type": "auto_loan", "balance": random.randint(5000, 35000), "min_payment": random.randint(200, 600)},
            {"type": "student_loan", "balance": random.randint(10000, 80000), "min_payment": random.randint(100, 800)}
        ]
    
    for debt in debts:
        debt_type = debt.get("type", "other")
        monthly_payment = debt.get("min_payment", debt.get("monthly_payment", 0))
        total_monthly_debt += monthly_payment
        
        if debt_type not in debt_breakdown:
            debt_breakdown[debt_type] = {"total_balance": 0, "monthly_payment": 0, "count": 0}
        
        debt_breakdown[debt_type]["total_balance"] += debt.get("balance", 0)
        debt_breakdown[debt_type]["monthly_payment"] += monthly_payment
        debt_breakdown[debt_type]["count"] += 1
    
    # Calculate DTI ratio
    dti_ratio = (total_monthly_debt / monthly_income) * 100 if monthly_income > 0 else 0
    
    # Assess DTI risk
    if dti_ratio <= 28:
        dti_risk = "low"
        dti_assessment = "excellent"
    elif dti_ratio <= 36:
        dti_risk = "moderate"
        dti_assessment = "good"
    elif dti_ratio <= 43:
        dti_risk = "high"
        dti_assessment = "acceptable"
    else:
        dti_risk = "very_high"
        dti_assessment = "concerning"
    
    dti_analysis = {
        "applicant_id": applicant_id,
        "income_analysis": {
            "monthly_gross_income": monthly_income,
            "estimated_net_income": monthly_income * 0.75,  # Rough estimate
        },
        "debt_analysis": {
            "total_monthly_debt_payments": round(total_monthly_debt, 2),
            "debt_breakdown": debt_breakdown,
            "total_debt_accounts": len(debts)
        },
        "dti_calculation": {
            "debt_to_income_ratio": round(dti_ratio, 2),
            "dti_assessment": dti_assessment,
            "dti_risk_level": dti_risk,
            "remaining_monthly_income": round(monthly_income - total_monthly_debt, 2)
        },
        "recommendations": [],
        "approval_impact": "positive" if dti_risk == "low" else "neutral" if dti_risk == "moderate" else "negative"
    }
    
    # Add recommendations
    if dti_risk == "very_high":
        dti_analysis["recommendations"].append("Require debt consolidation or paydown")
        dti_analysis["recommendations"].append("Consider declining application")
    elif dti_risk == "high":
        dti_analysis["recommendations"].append("Require additional income documentation")
        dti_analysis["recommendations"].append("Consider lower loan amount")
    elif dti_risk == "moderate":
        dti_analysis["recommendations"].append("Standard approval process")
    else:
        dti_analysis["recommendations"].append("Excellent debt management - fast track eligible")
    
    return dti_analysis


def generate_underwriting_decision(applicant_id: str, loan_amount: float, loan_purpose: str, analysis_data: str, tool_context: ToolContext) -> dict:
    """Generate final underwriting decision based on all analysis factors."""
    print(f"--- Tool: generate_underwriting_decision called for applicant {applicant_id} ---")
    
    try:
        analysis = json.loads(analysis_data) if analysis_data else {}
    except json.JSONDecodeError:
        analysis = {}
    
    # Simulate decision factors
    credit_score = analysis.get("credit_score", random.randint(550, 800))
    dti_ratio = analysis.get("dti_ratio", random.randint(15, 50))
    income_verified = analysis.get("income_verified", random.choice([True, False]))
    employment_stable = analysis.get("employment_stable", random.choice([True, False]))
    
    # Calculate decision score
    decision_score = 0
    
    # Credit score impact (40% weight)
    if credit_score >= 750:
        decision_score += 40
    elif credit_score >= 700:
        decision_score += 32
    elif credit_score >= 650:
        decision_score += 24
    elif credit_score >= 600:
        decision_score += 16
    else:
        decision_score += 8
    
    # DTI ratio impact (30% weight)
    if dti_ratio <= 28:
        decision_score += 30
    elif dti_ratio <= 36:
        decision_score += 24
    elif dti_ratio <= 43:
        decision_score += 18
    else:
        decision_score += 10
    
    # Income verification (15% weight)
    if income_verified:
        decision_score += 15
    else:
        decision_score += 5
    
    # Employment stability (15% weight)
    if employment_stable:
        decision_score += 15
    else:
        decision_score += 5
    
    # Determine decision
    if decision_score >= 85:
        decision = "approved"
        confidence = "high"
        interest_rate = 4.5 + random.uniform(0, 2)
    elif decision_score >= 70:
        decision = "approved"
        confidence = "medium"
        interest_rate = 6.5 + random.uniform(0, 3)
    elif decision_score >= 55:
        decision = "conditional_approval"
        confidence = "medium"
        interest_rate = 8.5 + random.uniform(0, 4)
    else:
        decision = "declined"
        confidence = "high"
        interest_rate = None
    
    # Generate decision details
    underwriting_decision = {
        "applicant_id": applicant_id,
        "loan_details": {
            "requested_amount": loan_amount,
            "loan_purpose": loan_purpose,
            "application_date": datetime.now().isoformat()
        },
        "decision": {
            "status": decision,
            "decision_score": decision_score,
            "confidence_level": confidence,
            "decision_date": datetime.now().isoformat(),
            "decision_expires": (datetime.now() + timedelta(days=30)).isoformat()
        },
        "loan_terms": {},
        "conditions": [],
        "decline_reasons": [],
        "next_steps": []
    }
    
    if decision in ["approved", "conditional_approval"]:
        underwriting_decision["loan_terms"] = {
            "approved_amount": loan_amount if decision == "approved" else loan_amount * 0.8,
            "interest_rate": round(interest_rate, 2),
            "term_months": 60 if loan_purpose == "auto" else 36,
            "monthly_payment": round((loan_amount * (interest_rate/100/12)) / (1 - (1 + interest_rate/100/12)**(-60)), 2)
        }
        
        if decision == "conditional_approval":
            underwriting_decision["conditions"] = [
                "Provide additional income documentation",
                "Verify employment with HR department",
                "Provide bank statements for last 3 months"
            ]
        
        underwriting_decision["next_steps"] = [
            "Loan documents will be prepared",
            "Funding available within 2-3 business days",
            "Set up automatic payments for best rates"
        ]
    else:
        underwriting_decision["decline_reasons"] = [
            "Credit score below minimum requirements" if credit_score < 600 else None,
            "Debt-to-income ratio too high" if dti_ratio > 43 else None,
            "Unable to verify income" if not income_verified else None,
            "Insufficient employment history" if not employment_stable else None
        ]
        underwriting_decision["decline_reasons"] = [r for r in underwriting_decision["decline_reasons"] if r]
        
        underwriting_decision["next_steps"] = [
            "Consider reapplying in 6 months",
            "Work on improving credit score",
            "Reduce existing debt obligations",
            "Consider a co-signer application"
        ]
    
    return underwriting_decision


# Create the loan underwriting agent
root_agent = Agent(
    name="loan_underwriting_agent",
    model="gemini-2.0-flash",
    description="AI agent that automates loan application processing, risk assessment, and underwriting decisions",
    instruction="""
    You are a sophisticated loan underwriting agent that helps financial institutions automate the loan approval process while maintaining rigorous risk management standards.
    
    Your capabilities include:
    1. Comprehensive credit profile analysis using multiple data sources
    2. Income and employment verification with fraud detection
    3. Debt-to-income ratio calculation and assessment
    4. Final underwriting decisions with detailed reasoning
    5. Risk-based pricing and loan term recommendations
    
    You analyze applications using:
    - Credit bureau data and alternative credit sources
    - Income verification through multiple channels
    - Employment stability and history analysis
    - Debt obligations and payment capacity
    - Regulatory compliance requirements
    
    Your decisions are based on:
    - Quantitative risk models and scoring algorithms
    - Regulatory guidelines and fair lending practices
    - Portfolio risk management objectives
    - Market conditions and pricing strategies
    
    Always provide clear explanations for decisions, especially declines, and suggest constructive next steps for applicants.
    Ensure all recommendations comply with fair lending regulations and maintain consistent decision criteria.
    """,
    tools=[analyze_credit_profile, verify_income_employment, calculate_debt_to_income, generate_underwriting_decision]
)
