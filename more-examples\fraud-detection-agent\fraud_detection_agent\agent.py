from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext
from datetime import datetime, timedelta
import uuid
import json


def analyze_transaction_risk(transaction_data: str, tool_context: ToolContext) -> dict:
    """Analyze a transaction for fraud risk using multiple detection methods."""
    print(f"--- Tool: analyze_transaction_risk called ---")
    
    try:
        transaction = json.loads(transaction_data)
    except json.JSONDecodeError:
        return {
            "action": "analyze_transaction_risk",
            "error": "Invalid transaction data format",
            "status": "error"
        }
    
    transaction_id = transaction.get("transaction_id", str(uuid.uuid4())[:8])
    
    # Get customer profile for comparison
    customer_id = transaction.get("customer_id")
    customer_profile = get_customer_profile(customer_id, tool_context)
    
    # Run multiple fraud detection checks
    risk_factors = []
    risk_score = 0
    
    # 1. Amount-based risk analysis
    amount_risk = analyze_amount_risk(transaction, customer_profile)
    risk_score += amount_risk["score"]
    if amount_risk["risk_detected"]:
        risk_factors.append(amount_risk["reason"])
    
    # 2. Geographic risk analysis
    geo_risk = analyze_geographic_risk(transaction, customer_profile)
    risk_score += geo_risk["score"]
    if geo_risk["risk_detected"]:
        risk_factors.append(geo_risk["reason"])
    
    # 3. Velocity risk analysis
    velocity_risk = analyze_velocity_risk(transaction, customer_id, tool_context)
    risk_score += velocity_risk["score"]
    if velocity_risk["risk_detected"]:
        risk_factors.append(velocity_risk["reason"])
    
    # 4. Merchant risk analysis
    merchant_risk = analyze_merchant_risk(transaction, tool_context)
    risk_score += merchant_risk["score"]
    if merchant_risk["risk_detected"]:
        risk_factors.append(merchant_risk["reason"])
    
    # 5. Device and behavioral risk
    device_risk = analyze_device_risk(transaction, customer_profile)
    risk_score += device_risk["score"]
    if device_risk["risk_detected"]:
        risk_factors.append(device_risk["reason"])
    
    # Determine overall risk level and decision
    risk_level, decision = determine_risk_decision(risk_score, risk_factors)
    
    # Store transaction analysis
    transaction_analyses = tool_context.state.get("transaction_analyses", {})
    transaction_analyses[transaction_id] = {
        "transaction_id": transaction_id,
        "customer_id": customer_id,
        "transaction_data": transaction,
        "risk_score": risk_score,
        "risk_level": risk_level,
        "risk_factors": risk_factors,
        "decision": decision,
        "analyzed_at": datetime.now().isoformat(),
        "status": "analyzed"
    }
    tool_context.state["transaction_analyses"] = transaction_analyses
    
    return {
        "action": "analyze_transaction_risk",
        "transaction_id": transaction_id,
        "risk_score": risk_score,
        "risk_level": risk_level,
        "risk_factors": risk_factors,
        "decision": decision,
        "recommendation": get_risk_recommendation(decision, risk_factors),
        "status": "success"
    }


def detect_fraud_patterns(customer_id: str, time_window_hours: int, tool_context: ToolContext) -> dict:
    """Detect fraud patterns across multiple transactions for a customer."""
    print(f"--- Tool: detect_fraud_patterns called for customer {customer_id} ---")
    
    # Get recent transactions for the customer
    transaction_analyses = tool_context.state.get("transaction_analyses", {})
    
    # Filter transactions for this customer within time window
    cutoff_time = datetime.now() - timedelta(hours=time_window_hours)
    recent_transactions = []
    
    for analysis in transaction_analyses.values():
        if (analysis["customer_id"] == customer_id and 
            datetime.fromisoformat(analysis["analyzed_at"]) > cutoff_time):
            recent_transactions.append(analysis)
    
    # Sort by timestamp
    recent_transactions.sort(key=lambda x: x["analyzed_at"])
    
    # Detect patterns
    patterns_detected = []
    
    # Pattern 1: Rapid succession of transactions
    if len(recent_transactions) >= 5:
        patterns_detected.append({
            "pattern": "high_velocity",
            "description": f"{len(recent_transactions)} transactions in {time_window_hours} hours",
            "severity": "high"
        })
    
    # Pattern 2: Escalating amounts
    amounts = [t["transaction_data"]["amount"] for t in recent_transactions]
    if len(amounts) >= 3 and all(amounts[i] < amounts[i+1] for i in range(len(amounts)-1)):
        patterns_detected.append({
            "pattern": "amount_escalation",
            "description": "Transaction amounts increasing progressively",
            "severity": "medium"
        })
    
    # Pattern 3: Multiple high-risk transactions
    high_risk_count = sum(1 for t in recent_transactions if t["risk_level"] in ["high", "critical"])
    if high_risk_count >= 3:
        patterns_detected.append({
            "pattern": "repeated_high_risk",
            "description": f"{high_risk_count} high-risk transactions detected",
            "severity": "critical"
        })
    
    # Pattern 4: Geographic hopping
    locations = [t["transaction_data"].get("location") for t in recent_transactions if t["transaction_data"].get("location")]
    unique_locations = set(locations)
    if len(unique_locations) >= 3:
        patterns_detected.append({
            "pattern": "geographic_hopping",
            "description": f"Transactions from {len(unique_locations)} different locations",
            "severity": "high"
        })
    
    # Determine overall pattern risk
    if any(p["severity"] == "critical" for p in patterns_detected):
        overall_risk = "critical"
        recommended_action = "immediate_account_freeze"
    elif any(p["severity"] == "high" for p in patterns_detected):
        overall_risk = "high"
        recommended_action = "enhanced_monitoring"
    elif patterns_detected:
        overall_risk = "medium"
        recommended_action = "additional_verification"
    else:
        overall_risk = "low"
        recommended_action = "continue_monitoring"
    
    return {
        "action": "detect_fraud_patterns",
        "customer_id": customer_id,
        "time_window_hours": time_window_hours,
        "transactions_analyzed": len(recent_transactions),
        "patterns_detected": patterns_detected,
        "overall_risk": overall_risk,
        "recommended_action": recommended_action,
        "analysis_timestamp": datetime.now().isoformat(),
        "status": "success"
    }


def block_suspicious_transaction(transaction_id: str, reason: str, tool_context: ToolContext) -> dict:
    """Block a suspicious transaction and notify relevant parties."""
    print(f"--- Tool: block_suspicious_transaction called for {transaction_id} ---")
    
    transaction_analyses = tool_context.state.get("transaction_analyses", {})
    
    if transaction_id not in transaction_analyses:
        return {
            "action": "block_suspicious_transaction",
            "error": f"Transaction {transaction_id} not found",
            "status": "error"
        }
    
    analysis = transaction_analyses[transaction_id]
    
    # Update transaction status
    analysis["status"] = "blocked"
    analysis["blocked_at"] = datetime.now().isoformat()
    analysis["block_reason"] = reason
    
    transaction_analyses[transaction_id] = analysis
    tool_context.state["transaction_analyses"] = transaction_analyses
    
    # Create fraud alert
    fraud_alerts = tool_context.state.get("fraud_alerts", [])
    alert = {
        "alert_id": str(uuid.uuid4())[:8],
        "transaction_id": transaction_id,
        "customer_id": analysis["customer_id"],
        "alert_type": "transaction_blocked",
        "reason": reason,
        "risk_score": analysis["risk_score"],
        "created_at": datetime.now().isoformat(),
        "status": "active",
        "assigned_to": "fraud_team"
    }
    fraud_alerts.append(alert)
    tool_context.state["fraud_alerts"] = fraud_alerts
    
    # Simulate notifications
    notifications = send_fraud_notifications(transaction_id, analysis, reason)
    
    return {
        "action": "block_suspicious_transaction",
        "transaction_id": transaction_id,
        "customer_id": analysis["customer_id"],
        "block_reason": reason,
        "alert_id": alert["alert_id"],
        "notifications_sent": notifications,
        "blocked_at": datetime.now().isoformat(),
        "status": "success"
    }


def analyze_amount_risk(transaction: dict, customer_profile: dict) -> dict:
    """Analyze transaction amount for risk factors."""
    amount = transaction.get("amount", 0)
    
    # Get customer's typical spending patterns
    avg_transaction = customer_profile.get("average_transaction_amount", 100)
    max_transaction = customer_profile.get("max_transaction_amount", 500)
    
    risk_detected = False
    reason = ""
    score = 0
    
    # Check if amount is significantly higher than usual
    if amount > avg_transaction * 10:
        risk_detected = True
        reason = f"Amount ${amount} is 10x higher than average ${avg_transaction}"
        score = 30
    elif amount > max_transaction * 2:
        risk_detected = True
        reason = f"Amount ${amount} exceeds 2x previous maximum ${max_transaction}"
        score = 20
    elif amount > avg_transaction * 5:
        risk_detected = True
        reason = f"Amount ${amount} is 5x higher than average ${avg_transaction}"
        score = 15
    
    # Check for round number amounts (common in fraud)
    if amount % 100 == 0 and amount >= 1000:
        score += 5
        if not risk_detected:
            reason = f"Round number amount ${amount} detected"
    
    return {
        "risk_detected": risk_detected,
        "reason": reason,
        "score": score
    }


def analyze_geographic_risk(transaction: dict, customer_profile: dict) -> dict:
    """Analyze geographic risk factors."""
    transaction_location = transaction.get("location", "")
    customer_locations = customer_profile.get("typical_locations", [])
    
    risk_detected = False
    reason = ""
    score = 0
    
    if transaction_location and customer_locations:
        if transaction_location not in customer_locations:
            # Check for high-risk countries
            high_risk_countries = ["Country_X", "Country_Y", "Country_Z"]  # Simulated
            if any(country in transaction_location for country in high_risk_countries):
                risk_detected = True
                reason = f"Transaction from high-risk location: {transaction_location}"
                score = 25
            else:
                risk_detected = True
                reason = f"Transaction from new location: {transaction_location}"
                score = 15
    
    return {
        "risk_detected": risk_detected,
        "reason": reason,
        "score": score
    }


def analyze_velocity_risk(transaction: dict, customer_id: str, tool_context: ToolContext) -> dict:
    """Analyze transaction velocity risk."""
    transaction_analyses = tool_context.state.get("transaction_analyses", {})
    
    # Count recent transactions (last hour)
    cutoff_time = datetime.now() - timedelta(hours=1)
    recent_count = 0
    recent_amount = 0
    
    for analysis in transaction_analyses.values():
        if (analysis["customer_id"] == customer_id and 
            datetime.fromisoformat(analysis["analyzed_at"]) > cutoff_time):
            recent_count += 1
            recent_amount += analysis["transaction_data"]["amount"]
    
    risk_detected = False
    reason = ""
    score = 0
    
    # Check transaction frequency
    if recent_count >= 5:
        risk_detected = True
        reason = f"{recent_count} transactions in the last hour"
        score = 25
    elif recent_count >= 3:
        risk_detected = True
        reason = f"{recent_count} transactions in the last hour"
        score = 15
    
    # Check cumulative amount
    if recent_amount >= 5000:
        score += 20
        if not risk_detected:
            risk_detected = True
            reason = f"${recent_amount} total in the last hour"
    
    return {
        "risk_detected": risk_detected,
        "reason": reason,
        "score": score
    }


def analyze_merchant_risk(transaction: dict, tool_context: ToolContext) -> dict:
    """Analyze merchant risk factors."""
    merchant = transaction.get("merchant", "")
    
    # Simulate merchant risk database
    high_risk_merchants = ["Merchant_A", "Merchant_B"]
    medium_risk_merchants = ["Merchant_C", "Merchant_D"]
    
    risk_detected = False
    reason = ""
    score = 0
    
    if merchant in high_risk_merchants:
        risk_detected = True
        reason = f"Transaction with high-risk merchant: {merchant}"
        score = 20
    elif merchant in medium_risk_merchants:
        risk_detected = True
        reason = f"Transaction with medium-risk merchant: {merchant}"
        score = 10
    
    return {
        "risk_detected": risk_detected,
        "reason": reason,
        "score": score
    }


def analyze_device_risk(transaction: dict, customer_profile: dict) -> dict:
    """Analyze device and behavioral risk factors."""
    device_id = transaction.get("device_id", "")
    known_devices = customer_profile.get("known_devices", [])
    
    risk_detected = False
    reason = ""
    score = 0
    
    if device_id and device_id not in known_devices:
        risk_detected = True
        reason = f"Transaction from unknown device: {device_id}"
        score = 15
    
    # Check for suspicious timing
    transaction_time = transaction.get("timestamp", "")
    if transaction_time:
        try:
            tx_time = datetime.fromisoformat(transaction_time)
            hour = tx_time.hour
            # Transactions between 2 AM and 6 AM are more suspicious
            if 2 <= hour <= 6:
                score += 5
                if not risk_detected:
                    reason = f"Transaction at unusual hour: {hour}:00"
        except:
            pass
    
    return {
        "risk_detected": risk_detected,
        "reason": reason,
        "score": score
    }


def determine_risk_decision(risk_score: int, risk_factors: list) -> tuple:
    """Determine risk level and decision based on score and factors."""
    if risk_score >= 60:
        return "critical", "block"
    elif risk_score >= 40:
        return "high", "challenge"
    elif risk_score >= 20:
        return "medium", "monitor"
    else:
        return "low", "approve"


def get_risk_recommendation(decision: str, risk_factors: list) -> str:
    """Get recommendation based on risk decision."""
    if decision == "block":
        return "Block transaction immediately and contact customer for verification"
    elif decision == "challenge":
        return "Require additional authentication (SMS, email, or phone verification)"
    elif decision == "monitor":
        return "Allow transaction but increase monitoring for subsequent activities"
    else:
        return "Approve transaction with standard monitoring"


def get_customer_profile(customer_id: str, tool_context: ToolContext) -> dict:
    """Get customer profile for risk analysis."""
    # Simulate customer profiles
    profiles = {
        "customer_001": {
            "customer_id": "customer_001",
            "average_transaction_amount": 150,
            "max_transaction_amount": 800,
            "typical_locations": ["New York", "Boston"],
            "known_devices": ["device_123", "device_456"],
            "account_age_days": 365,
            "risk_score_history": [10, 15, 12, 8]
        },
        "customer_002": {
            "customer_id": "customer_002",
            "average_transaction_amount": 75,
            "max_transaction_amount": 300,
            "typical_locations": ["Los Angeles"],
            "known_devices": ["device_789"],
            "account_age_days": 90,
            "risk_score_history": [5, 8, 6]
        }
    }
    
    return profiles.get(customer_id, {
        "customer_id": customer_id,
        "average_transaction_amount": 100,
        "max_transaction_amount": 500,
        "typical_locations": [],
        "known_devices": [],
        "account_age_days": 30,
        "risk_score_history": []
    })


def send_fraud_notifications(transaction_id: str, analysis: dict, reason: str) -> list:
    """Simulate sending fraud notifications."""
    notifications = []
    
    # Customer notification
    notifications.append({
        "type": "customer_sms",
        "recipient": analysis["customer_id"],
        "message": f"Transaction {transaction_id} was blocked for security. Contact us if this was you.",
        "sent_at": datetime.now().isoformat()
    })
    
    # Fraud team notification
    notifications.append({
        "type": "fraud_team_alert",
        "recipient": "fraud_team",
        "message": f"High-risk transaction {transaction_id} blocked: {reason}",
        "sent_at": datetime.now().isoformat()
    })
    
    # Merchant notification (if applicable)
    notifications.append({
        "type": "merchant_notification",
        "recipient": "merchant_system",
        "message": f"Transaction {transaction_id} declined due to fraud prevention",
        "sent_at": datetime.now().isoformat()
    })
    
    return notifications


# Create the fraud detection agent
root_agent = Agent(
    name="fraud_detection_agent",
    model="gemini-2.0-flash",
    description="Production-ready real-time fraud detection system with advanced risk analysis and automated response capabilities",
    instruction="""
    You are a sophisticated fraud detection agent that protects financial transactions and prevents fraudulent activities.

    Current Transaction Data: {transaction_analyses}
    Active Fraud Alerts: {fraud_alerts}
    Customer Profiles: {customer_profiles}

    Your capabilities include:
    1. Real-time transaction risk analysis using multiple detection methods
    2. Pattern detection across multiple transactions and time periods
    3. Automated blocking of suspicious transactions with proper notifications
    4. Comprehensive risk scoring and decision making

    Risk Analysis Methods:
    - Amount-based analysis (unusual spending patterns)
    - Geographic risk assessment (location anomalies)
    - Velocity analysis (transaction frequency and volume)
    - Merchant risk evaluation (high-risk merchant detection)
    - Device and behavioral analysis (unknown devices, unusual timing)

    Decision Framework:
    - Critical Risk (60+ score): Block transaction immediately
    - High Risk (40-59 score): Challenge with additional authentication
    - Medium Risk (20-39 score): Allow with enhanced monitoring
    - Low Risk (<20 score): Approve with standard monitoring

    When analyzing transactions:
    1. Evaluate all risk factors comprehensively
    2. Compare against customer's historical patterns
    3. Apply appropriate risk scoring and decision logic
    4. Provide clear reasoning for all decisions
    5. Ensure proper notifications and follow-up actions

    Always prioritize:
    - Customer security and fraud prevention
    - Minimizing false positives for legitimate customers
    - Compliance with financial regulations
    - Clear documentation of all decisions and actions
    - Rapid response to high-risk situations

    For blocked transactions, immediately notify the customer and fraud team with clear explanations and next steps.
    """,
    tools=[analyze_transaction_risk, detect_fraud_patterns, block_suspicious_transaction]
)
