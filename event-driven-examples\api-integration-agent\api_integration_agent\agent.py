from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Optional
import time


def configure_api_endpoint(name: str, base_url: str, auth_type: str, auth_credentials: str, tool_context: ToolContext) -> dict:
    """Configure a new API endpoint for integration.
    
    Args:
        name: Name identifier for the API
        base_url: Base URL of the API
        auth_type: Authentication type ('api_key', 'bearer', 'basic', 'oauth')
        auth_credentials: Authentication credentials
        tool_context: Context for accessing session state
        
    Returns:
        A dictionary with configuration confirmation
    """
    print(f"--- Tool: configure_api_endpoint called for {name} ---")
    
    # Validate inputs
    if not base_url.startswith(('http://', 'https://')):
        return {
            "action": "configure_api_endpoint",
            "error": "Base URL must start with http:// or https://",
            "status": "error"
        }
    
    supported_auth_types = ['api_key', 'bearer', 'basic', 'oauth', 'none']
    if auth_type not in supported_auth_types:
        return {
            "action": "configure_api_endpoint",
            "error": f"Auth type must be one of: {supported_auth_types}",
            "status": "error"
        }
    
    # Store API configuration
    api_configs = tool_context.state.get("api_configurations", {})
    
    api_configs[name] = {
        "base_url": base_url,
        "auth_type": auth_type,
        "auth_credentials": auth_credentials,
        "configured_at": datetime.now().isoformat(),
        "requests_made": 0,
        "last_request_at": None,
        "rate_limit_remaining": 1000,  # Default rate limit
        "status": "active"
    }
    
    tool_context.state["api_configurations"] = api_configs
    
    return {
        "action": "configure_api_endpoint",
        "name": name,
        "base_url": base_url,
        "auth_type": auth_type,
        "message": f"API endpoint '{name}' configured successfully",
        "status": "success"
    }


def make_api_request(api_name: str, endpoint: str, method: str, payload: Optional[str] = None, headers: Optional[str] = None, tool_context: ToolContext = None) -> dict:
    """Make an API request to a configured endpoint.
    
    Args:
        api_name: Name of the configured API
        endpoint: API endpoint path
        method: HTTP method (GET, POST, PUT, DELETE)
        payload: Optional JSON payload for POST/PUT requests
        headers: Optional additional headers as JSON string
        tool_context: Context for accessing session state
        
    Returns:
        A dictionary with API response
    """
    print(f"--- Tool: make_api_request called for {api_name} {method} {endpoint} ---")
    
    # Get API configuration
    api_configs = tool_context.state.get("api_configurations", {})
    
    if api_name not in api_configs:
        return {
            "action": "make_api_request",
            "error": f"API configuration '{api_name}' not found",
            "status": "error"
        }
    
    api_config = api_configs[api_name]
    
    # Check rate limiting
    if api_config["rate_limit_remaining"] <= 0:
        return {
            "action": "make_api_request",
            "error": "Rate limit exceeded",
            "status": "rate_limited"
        }
    
    # Validate method
    allowed_methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
    if method.upper() not in allowed_methods:
        return {
            "action": "make_api_request",
            "error": f"Method must be one of: {allowed_methods}",
            "status": "error"
        }
    
    try:
        # Parse additional headers if provided
        additional_headers = {}
        if headers:
            additional_headers = json.loads(headers)
        
        # Parse payload if provided
        request_payload = None
        if payload:
            request_payload = json.loads(payload)
        
        # Simulate API request
        response = simulate_api_request(api_config, endpoint, method.upper(), request_payload, additional_headers)
        
        # Update API statistics
        api_config["requests_made"] += 1
        api_config["last_request_at"] = datetime.now().isoformat()
        api_config["rate_limit_remaining"] -= 1
        api_configs[api_name] = api_config
        tool_context.state["api_configurations"] = api_configs
        
        return {
            "action": "make_api_request",
            "api_name": api_name,
            "endpoint": endpoint,
            "method": method.upper(),
            "response": response,
            "requested_at": datetime.now().isoformat(),
            "status": "success"
        }
        
    except json.JSONDecodeError as e:
        return {
            "action": "make_api_request",
            "error": f"JSON parsing error: {str(e)}",
            "status": "error"
        }
    except Exception as e:
        return {
            "action": "make_api_request",
            "error": f"Request error: {str(e)}",
            "status": "error"
        }


def simulate_api_request(api_config: Dict[str, Any], endpoint: str, method: str, payload: Optional[Dict], headers: Dict[str, str]) -> dict:
    """Simulate an API request and return mock response.
    
    Args:
        api_config: API configuration
        endpoint: API endpoint
        method: HTTP method
        payload: Request payload
        headers: Request headers
        
    Returns:
        A dictionary with simulated API response
    """
    # Simulate network delay
    time.sleep(0.1)
    
    # Generate mock response based on endpoint and method
    if method == "GET":
        if "users" in endpoint:
            mock_data = {
                "users": [
                    {"id": 1, "name": "John Doe", "email": "<EMAIL>"},
                    {"id": 2, "name": "Jane Smith", "email": "<EMAIL>"}
                ],
                "total": 2,
                "page": 1
            }
        elif "products" in endpoint:
            mock_data = {
                "products": [
                    {"id": 1, "name": "Laptop", "price": 999.99},
                    {"id": 2, "name": "Mouse", "price": 29.99}
                ],
                "total": 2
            }
        else:
            mock_data = {
                "message": "GET request successful",
                "endpoint": endpoint,
                "timestamp": datetime.now().isoformat()
            }
    
    elif method == "POST":
        mock_data = {
            "message": "Resource created successfully",
            "id": 123,
            "created_at": datetime.now().isoformat(),
            "data": payload
        }
    
    elif method == "PUT":
        mock_data = {
            "message": "Resource updated successfully",
            "updated_at": datetime.now().isoformat(),
            "data": payload
        }
    
    elif method == "DELETE":
        mock_data = {
            "message": "Resource deleted successfully",
            "deleted_at": datetime.now().isoformat()
        }
    
    else:
        mock_data = {
            "message": f"{method} request processed",
            "endpoint": endpoint
        }
    
    return {
        "status_code": 200,
        "headers": {
            "content-type": "application/json",
            "x-rate-limit-remaining": "999",
            "x-response-time": "100ms"
        },
        "data": mock_data
    }


def handle_api_authentication(api_name: str, auth_type: str, credentials: str, tool_context: ToolContext) -> dict:
    """Handle API authentication and token refresh.
    
    Args:
        api_name: Name of the API
        auth_type: Type of authentication
        credentials: Authentication credentials
        tool_context: Context for accessing session state
        
    Returns:
        A dictionary with authentication status
    """
    print(f"--- Tool: handle_api_authentication called for {api_name} ---")
    
    api_configs = tool_context.state.get("api_configurations", {})
    
    if api_name not in api_configs:
        return {
            "action": "handle_api_authentication",
            "error": f"API configuration '{api_name}' not found",
            "status": "error"
        }
    
    # Simulate authentication process
    if auth_type == "oauth":
        # Simulate OAuth token refresh
        auth_result = {
            "access_token": "mock_access_token_" + str(int(time.time())),
            "refresh_token": "mock_refresh_token_" + str(int(time.time())),
            "expires_in": 3600,
            "token_type": "Bearer"
        }
    elif auth_type == "api_key":
        auth_result = {
            "api_key": credentials,
            "key_status": "valid",
            "expires_in": None
        }
    elif auth_type == "bearer":
        auth_result = {
            "bearer_token": credentials,
            "token_status": "valid",
            "expires_in": 7200
        }
    else:
        auth_result = {
            "auth_type": auth_type,
            "status": "authenticated"
        }
    
    # Update API configuration with new auth info
    api_config = api_configs[api_name]
    api_config["auth_credentials"] = credentials
    api_config["last_auth_at"] = datetime.now().isoformat()
    api_config["auth_expires_at"] = (datetime.now() + timedelta(seconds=auth_result.get("expires_in", 3600))).isoformat()
    api_configs[api_name] = api_config
    tool_context.state["api_configurations"] = api_configs
    
    return {
        "action": "handle_api_authentication",
        "api_name": api_name,
        "auth_type": auth_type,
        "auth_result": auth_result,
        "authenticated_at": datetime.now().isoformat(),
        "status": "success"
    }


def list_api_configurations(tool_context: ToolContext) -> dict:
    """List all configured API endpoints.
    
    Args:
        tool_context: Context for accessing session state
        
    Returns:
        A dictionary with all API configurations
    """
    print("--- Tool: list_api_configurations called ---")
    
    api_configs = tool_context.state.get("api_configurations", {})
    
    # Remove sensitive information for display
    safe_configs = {}
    for api_name, config in api_configs.items():
        safe_configs[api_name] = {
            "base_url": config["base_url"],
            "auth_type": config["auth_type"],
            "configured_at": config["configured_at"],
            "requests_made": config["requests_made"],
            "last_request_at": config["last_request_at"],
            "rate_limit_remaining": config["rate_limit_remaining"],
            "status": config["status"]
        }
    
    return {
        "action": "list_api_configurations",
        "apis": safe_configs,
        "total_apis": len(api_configs)
    }


# Create the API integration agent
root_agent = Agent(
    name="api_integration_agent",
    model="gemini-2.0-flash",
    description="Agent that integrates with external APIs and handles various response formats with best practices",
    instruction="""
    You are an API integration agent that specializes in connecting with external web services and APIs.
    
    Your capabilities include:
    1. API endpoint configuration and management
    2. Multiple authentication methods (API key, OAuth, Bearer token, Basic auth)
    3. HTTP request handling (GET, POST, PUT, DELETE, PATCH)
    4. Response processing and validation
    5. Error handling and retry mechanisms
    6. Rate limiting and throttling management
    
    Supported authentication types:
    - api_key: Simple API key authentication
    - bearer: Bearer token authentication
    - basic: Basic HTTP authentication
    - oauth: OAuth 2.0 flow
    - none: No authentication required
    
    When users want to work with APIs:
    - Use configure_api_endpoint to set up new API integrations
    - Use make_api_request to send HTTP requests to configured APIs
    - Use handle_api_authentication for managing authentication tokens
    - Use list_api_configurations to view all configured APIs
    
    Always emphasize best practices:
    - Secure credential handling
    - Proper error handling and retries
    - Rate limit compliance
    - Response validation
    - Logging and monitoring
    
    For demonstrations, you can simulate API responses with realistic mock data.
    Help users understand REST API concepts and integration patterns.
    """,
    tools=[configure_api_endpoint, make_api_request, handle_api_authentication, list_api_configurations]
)
