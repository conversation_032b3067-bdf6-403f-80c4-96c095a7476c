from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext
from datetime import datetime, timedelta
import uuid


def schedule_task(task_name: str, scheduled_time: str, task_description: str, tool_context: ToolContext) -> dict:
    """Schedule a task to run at a specific time."""
    print(f"--- Tool: schedule_task called for {task_name} at {scheduled_time} ---")
    
    try:
        # Parse the scheduled time
        scheduled_dt = datetime.fromisoformat(scheduled_time.replace('Z', '+00:00'))
        
        # Create task
        scheduled_tasks = tool_context.state.get("scheduled_tasks", {})
        task_id = str(uuid.uuid4())[:8]
        
        scheduled_tasks[task_id] = {
            "name": task_name,
            "description": task_description,
            "scheduled_time": scheduled_time,
            "created_at": datetime.now().isoformat(),
            "status": "scheduled",
            "recurring": False
        }
        
        tool_context.state["scheduled_tasks"] = scheduled_tasks
        
        return {
            "action": "schedule_task",
            "task_id": task_id,
            "task_name": task_name,
            "scheduled_time": scheduled_time,
            "message": f"Task '{task_name}' scheduled for {scheduled_time}",
            "status": "success"
        }
        
    except ValueError as e:
        return {
            "action": "schedule_task",
            "error": f"Invalid time format: {str(e)}",
            "status": "error"
        }


def create_recurring_task(task_name: str, start_time: str, interval: str, task_description: str, tool_context: ToolContext) -> dict:
    """Create a recurring task with specified interval."""
    print(f"--- Tool: create_recurring_task called for {task_name} ---")
    
    scheduled_tasks = tool_context.state.get("scheduled_tasks", {})
    task_id = str(uuid.uuid4())[:8]
    
    scheduled_tasks[task_id] = {
        "name": task_name,
        "description": task_description,
        "start_time": start_time,
        "interval": interval,
        "created_at": datetime.now().isoformat(),
        "status": "scheduled",
        "recurring": True,
        "next_run": start_time
    }
    
    tool_context.state["scheduled_tasks"] = scheduled_tasks
    
    return {
        "action": "create_recurring_task",
        "task_id": task_id,
        "task_name": task_name,
        "interval": interval,
        "message": f"Recurring task '{task_name}' created with {interval} interval",
        "status": "success"
    }


def list_scheduled_tasks(tool_context: ToolContext) -> dict:
    """List all scheduled tasks."""
    print("--- Tool: list_scheduled_tasks called ---")
    
    scheduled_tasks = tool_context.state.get("scheduled_tasks", {})
    
    return {
        "action": "list_scheduled_tasks",
        "tasks": scheduled_tasks,
        "total_tasks": len(scheduled_tasks),
        "status": "success"
    }


def cancel_task(task_id: str, tool_context: ToolContext) -> dict:
    """Cancel a scheduled task."""
    print(f"--- Tool: cancel_task called for {task_id} ---")
    
    scheduled_tasks = tool_context.state.get("scheduled_tasks", {})
    
    if task_id not in scheduled_tasks:
        return {
            "action": "cancel_task",
            "error": f"Task {task_id} not found",
            "status": "error"
        }
    
    task = scheduled_tasks[task_id]
    task["status"] = "cancelled"
    task["cancelled_at"] = datetime.now().isoformat()
    
    scheduled_tasks[task_id] = task
    tool_context.state["scheduled_tasks"] = scheduled_tasks
    
    return {
        "action": "cancel_task",
        "task_id": task_id,
        "task_name": task["name"],
        "message": f"Task '{task['name']}' cancelled",
        "status": "success"
    }


# Create the scheduling agent
root_agent = Agent(
    name="scheduling_agent",
    model="gemini-2.0-flash",
    description="Agent that handles task scheduling, automation, and time-based operations",
    instruction="""
    You are a scheduling agent that helps users manage tasks, appointments, and automated workflows.
    
    Your capabilities include:
    1. Scheduling one-time tasks at specific times
    2. Creating recurring tasks with various intervals
    3. Managing task lists and schedules
    4. Cancelling and modifying scheduled tasks
    
    Current scheduled tasks: {scheduled_tasks}
    
    When users want to schedule something:
    - Use schedule_task for one-time tasks
    - Use create_recurring_task for repeating tasks
    - Use list_scheduled_tasks to show all scheduled items
    - Use cancel_task to remove scheduled tasks
    
    Always confirm scheduling details and provide clear feedback about when tasks are scheduled to run.
    """,
    tools=[schedule_task, create_recurring_task, list_scheduled_tasks, cancel_task]
)
