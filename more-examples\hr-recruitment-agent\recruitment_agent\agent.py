from google.adk.agents import Agent
from google.adk.tools.tool_context import <PERSON>lContext
from datetime import datetime, timedelta
import uuid
import json


def parse_resume(resume_content: str, candidate_email: str, tool_context: ToolContext) -> dict:
    """Parse resume content and extract structured candidate information."""
    print(f"--- Tool: parse_resume called for {candidate_email} ---")
    
    # Simulate advanced resume parsing
    candidate_id = f"CAND-{str(uuid.uuid4())[:8].upper()}"
    
    # Extract structured data (simulated based on common resume patterns)
    parsed_data = {
        "candidate_id": candidate_id,
        "email": candidate_email,
        "name": extract_name_from_resume(resume_content),
        "phone": extract_phone_from_resume(resume_content),
        "location": extract_location_from_resume(resume_content),
        "skills": extract_skills_from_resume(resume_content),
        "experience": extract_experience_from_resume(resume_content),
        "education": extract_education_from_resume(resume_content),
        "certifications": extract_certifications_from_resume(resume_content),
        "total_experience_years": calculate_total_experience(resume_content),
        "current_role": extract_current_role(resume_content),
        "salary_expectation": extract_salary_expectation(resume_content)
    }
    
    # Store parsed resume
    parsed_resumes = tool_context.state.get("parsed_resumes", {})
    parsed_resumes[candidate_id] = {
        "candidate_id": candidate_id,
        "raw_content": resume_content,
        "parsed_data": parsed_data,
        "parsed_at": datetime.now().isoformat(),
        "parsing_confidence": 0.92,
        "status": "parsed"
    }
    tool_context.state["parsed_resumes"] = parsed_resumes
    
    return {
        "action": "parse_resume",
        "candidate_id": candidate_id,
        "parsed_data": parsed_data,
        "parsing_confidence": 0.92,
        "extraction_summary": {
            "skills_found": len(parsed_data["skills"]),
            "experience_entries": len(parsed_data["experience"]),
            "education_entries": len(parsed_data["education"]),
            "total_years_experience": parsed_data["total_experience_years"]
        },
        "status": "success"
    }


def screen_candidate(candidate_id: str, job_requirements: str, tool_context: ToolContext) -> dict:
    """Screen candidate against job requirements and generate compatibility score."""
    print(f"--- Tool: screen_candidate called for {candidate_id} ---")
    
    parsed_resumes = tool_context.state.get("parsed_resumes", {})
    
    if candidate_id not in parsed_resumes:
        return {
            "action": "screen_candidate",
            "error": f"Candidate {candidate_id} not found",
            "status": "error"
        }
    
    try:
        job_reqs = json.loads(job_requirements)
    except json.JSONDecodeError:
        return {
            "action": "screen_candidate",
            "error": "Invalid job requirements format",
            "status": "error"
        }
    
    candidate_data = parsed_resumes[candidate_id]["parsed_data"]
    
    # Perform comprehensive screening
    screening_results = {
        "skills_match": evaluate_skills_match(candidate_data["skills"], job_reqs.get("required_skills", [])),
        "experience_match": evaluate_experience_match(candidate_data, job_reqs),
        "education_match": evaluate_education_match(candidate_data["education"], job_reqs.get("education_requirements", {})),
        "location_match": evaluate_location_match(candidate_data["location"], job_reqs.get("location", "")),
        "salary_match": evaluate_salary_match(candidate_data.get("salary_expectation"), job_reqs.get("salary_range", {})),
        "red_flags": detect_red_flags(candidate_data)
    }
    
    # Calculate overall compatibility score
    overall_score = calculate_compatibility_score(screening_results)
    
    # Determine screening decision
    decision = determine_screening_decision(overall_score, screening_results)
    
    # Store screening results
    screening_id = f"SCR-{str(uuid.uuid4())[:8].upper()}"
    candidate_screenings = tool_context.state.get("candidate_screenings", {})
    candidate_screenings[screening_id] = {
        "screening_id": screening_id,
        "candidate_id": candidate_id,
        "job_requirements": job_reqs,
        "screening_results": screening_results,
        "overall_score": overall_score,
        "decision": decision,
        "screened_at": datetime.now().isoformat(),
        "screener": "AI_Screening_Agent"
    }
    tool_context.state["candidate_screenings"] = candidate_screenings
    
    return {
        "action": "screen_candidate",
        "screening_id": screening_id,
        "candidate_id": candidate_id,
        "overall_score": overall_score,
        "decision": decision,
        "screening_results": screening_results,
        "next_steps": get_next_steps(decision, screening_results),
        "status": "success"
    }


def schedule_interview(candidate_id: str, interview_type: str, interviewer_preferences: str, tool_context: ToolContext) -> dict:
    """Schedule interview for qualified candidate."""
    print(f"--- Tool: schedule_interview called for {candidate_id} ---")
    
    parsed_resumes = tool_context.state.get("parsed_resumes", {})
    candidate_screenings = tool_context.state.get("candidate_screenings", {})
    
    if candidate_id not in parsed_resumes:
        return {
            "action": "schedule_interview",
            "error": f"Candidate {candidate_id} not found",
            "status": "error"
        }
    
    # Check if candidate passed screening
    candidate_passed_screening = any(
        screening["candidate_id"] == candidate_id and screening["decision"] in ["proceed", "strong_candidate"]
        for screening in candidate_screenings.values()
    )
    
    if not candidate_passed_screening:
        return {
            "action": "schedule_interview",
            "error": f"Candidate {candidate_id} has not passed initial screening",
            "status": "error"
        }
    
    try:
        preferences = json.loads(interviewer_preferences) if interviewer_preferences else {}
    except json.JSONDecodeError:
        preferences = {}
    
    candidate_data = parsed_resumes[candidate_id]["parsed_data"]
    
    # Determine interview panel based on role and interview type
    interview_panel = determine_interview_panel(interview_type, candidate_data["current_role"])
    
    # Find available time slots
    available_slots = find_available_interview_slots(interview_panel, preferences)
    
    # Create interview schedule
    interview_id = f"INT-{str(uuid.uuid4())[:8].upper()}"
    scheduled_interviews = tool_context.state.get("scheduled_interviews", {})
    
    selected_slot = available_slots[0] if available_slots else None
    
    scheduled_interviews[interview_id] = {
        "interview_id": interview_id,
        "candidate_id": candidate_id,
        "interview_type": interview_type,
        "interview_panel": interview_panel,
        "scheduled_time": selected_slot["datetime"] if selected_slot else None,
        "duration_minutes": selected_slot["duration"] if selected_slot else 60,
        "location": selected_slot["location"] if selected_slot else "Video Conference",
        "status": "scheduled" if selected_slot else "pending_scheduling",
        "created_at": datetime.now().isoformat(),
        "meeting_link": f"https://meet.company.com/interview/{interview_id}" if selected_slot else None
    }
    tool_context.state["scheduled_interviews"] = scheduled_interviews
    
    # Send notifications
    notifications = send_interview_notifications(interview_id, candidate_data, interview_panel, selected_slot)
    
    return {
        "action": "schedule_interview",
        "interview_id": interview_id,
        "candidate_id": candidate_id,
        "interview_type": interview_type,
        "scheduled_time": selected_slot["datetime"] if selected_slot else None,
        "interview_panel": interview_panel,
        "meeting_details": selected_slot if selected_slot else "Scheduling in progress",
        "notifications_sent": notifications,
        "status": "success" if selected_slot else "pending"
    }


def extract_name_from_resume(content: str) -> str:
    """Extract candidate name from resume content."""
    # Simulate name extraction
    lines = content.split('\n')
    for line in lines[:5]:  # Check first 5 lines
        if len(line.strip()) > 0 and len(line.strip().split()) <= 3:
            return line.strip()
    return "John Doe"  # Default if not found


def extract_phone_from_resume(content: str) -> str:
    """Extract phone number from resume content."""
    import re
    phone_pattern = r'(\+?1?[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})'
    match = re.search(phone_pattern, content)
    return match.group(0) if match else "******-0123"


def extract_location_from_resume(content: str) -> str:
    """Extract location from resume content."""
    # Simulate location extraction
    common_cities = ["New York", "San Francisco", "Los Angeles", "Chicago", "Boston", "Seattle"]
    for city in common_cities:
        if city in content:
            return f"{city}, USA"
    return "Remote"


def extract_skills_from_resume(content: str) -> list:
    """Extract skills from resume content."""
    # Simulate skills extraction
    all_skills = [
        "Python", "JavaScript", "React", "Node.js", "AWS", "Docker", "Kubernetes",
        "SQL", "MongoDB", "Git", "Agile", "Scrum", "Machine Learning", "Data Analysis",
        "Project Management", "Leadership", "Communication", "Problem Solving"
    ]
    
    found_skills = []
    content_lower = content.lower()
    for skill in all_skills:
        if skill.lower() in content_lower:
            found_skills.append(skill)
    
    return found_skills[:10]  # Return top 10 found skills


def extract_experience_from_resume(content: str) -> list:
    """Extract work experience from resume content."""
    # Simulate experience extraction
    return [
        {
            "company": "Tech Corp",
            "title": "Senior Software Engineer",
            "duration": "2021-2024",
            "description": "Led development of microservices architecture"
        },
        {
            "company": "StartupXYZ",
            "title": "Software Engineer",
            "duration": "2019-2021",
            "description": "Full-stack development using React and Node.js"
        }
    ]


def extract_education_from_resume(content: str) -> list:
    """Extract education information from resume content."""
    return [
        {
            "degree": "Bachelor of Science in Computer Science",
            "institution": "University of Technology",
            "graduation_year": "2019",
            "gpa": "3.8"
        }
    ]


def extract_certifications_from_resume(content: str) -> list:
    """Extract certifications from resume content."""
    return [
        {
            "name": "AWS Certified Solutions Architect",
            "issuer": "Amazon Web Services",
            "year": "2023"
        }
    ]


def calculate_total_experience(content: str) -> int:
    """Calculate total years of experience."""
    # Simulate experience calculation
    return 5  # Default 5 years


def extract_current_role(content: str) -> str:
    """Extract current role from resume."""
    return "Senior Software Engineer"


def extract_salary_expectation(content: str) -> dict:
    """Extract salary expectation if mentioned."""
    return {"min": 120000, "max": 150000, "currency": "USD"}


def evaluate_skills_match(candidate_skills: list, required_skills: list) -> dict:
    """Evaluate how well candidate skills match job requirements."""
    if not required_skills:
        return {"score": 100, "matched_skills": candidate_skills, "missing_skills": []}
    
    matched_skills = [skill for skill in required_skills if skill in candidate_skills]
    missing_skills = [skill for skill in required_skills if skill not in candidate_skills]
    
    match_percentage = (len(matched_skills) / len(required_skills)) * 100
    
    return {
        "score": round(match_percentage, 1),
        "matched_skills": matched_skills,
        "missing_skills": missing_skills,
        "additional_skills": [skill for skill in candidate_skills if skill not in required_skills]
    }


def evaluate_experience_match(candidate_data: dict, job_reqs: dict) -> dict:
    """Evaluate experience match against requirements."""
    required_years = job_reqs.get("min_experience_years", 0)
    candidate_years = candidate_data.get("total_experience_years", 0)
    
    if candidate_years >= required_years:
        score = min(100, (candidate_years / required_years) * 100)
    else:
        score = (candidate_years / required_years) * 100 if required_years > 0 else 100
    
    return {
        "score": round(score, 1),
        "candidate_years": candidate_years,
        "required_years": required_years,
        "meets_requirement": candidate_years >= required_years
    }


def evaluate_education_match(candidate_education: list, education_reqs: dict) -> dict:
    """Evaluate education match against requirements."""
    if not education_reqs:
        return {"score": 100, "meets_requirement": True}
    
    required_degree = education_reqs.get("degree_level", "").lower()
    
    # Check if candidate has required degree level
    degree_levels = {"bachelor": 1, "master": 2, "phd": 3, "doctorate": 3}
    
    candidate_max_level = 0
    for edu in candidate_education:
        degree = edu.get("degree", "").lower()
        for level_name, level_value in degree_levels.items():
            if level_name in degree:
                candidate_max_level = max(candidate_max_level, level_value)
    
    required_level = degree_levels.get(required_degree, 0)
    
    meets_requirement = candidate_max_level >= required_level
    score = 100 if meets_requirement else 50
    
    return {
        "score": score,
        "meets_requirement": meets_requirement,
        "candidate_education": candidate_education,
        "required_degree": required_degree
    }


def evaluate_location_match(candidate_location: str, job_location: str) -> dict:
    """Evaluate location compatibility."""
    if not job_location or "remote" in job_location.lower():
        return {"score": 100, "compatible": True, "reason": "Remote position"}
    
    if candidate_location.lower() == job_location.lower():
        return {"score": 100, "compatible": True, "reason": "Exact location match"}
    
    # Check if same city/state
    if any(part in job_location for part in candidate_location.split(", ")):
        return {"score": 80, "compatible": True, "reason": "Same region"}
    
    return {"score": 30, "compatible": False, "reason": "Location mismatch - relocation required"}


def evaluate_salary_match(candidate_expectation: dict, salary_range: dict) -> dict:
    """Evaluate salary expectation alignment."""
    if not candidate_expectation or not salary_range:
        return {"score": 100, "compatible": True, "reason": "No salary information provided"}
    
    candidate_min = candidate_expectation.get("min", 0)
    candidate_max = candidate_expectation.get("max", 0)
    job_min = salary_range.get("min", 0)
    job_max = salary_range.get("max", 0)
    
    # Check for overlap
    if candidate_min <= job_max and candidate_max >= job_min:
        overlap_size = min(candidate_max, job_max) - max(candidate_min, job_min)
        total_range = max(candidate_max, job_max) - min(candidate_min, job_min)
        overlap_percentage = (overlap_size / total_range) * 100 if total_range > 0 else 100
        
        return {
            "score": round(overlap_percentage, 1),
            "compatible": True,
            "reason": f"Salary ranges overlap by {overlap_percentage:.1f}%"
        }
    
    return {
        "score": 20,
        "compatible": False,
        "reason": "No salary range overlap"
    }


def detect_red_flags(candidate_data: dict) -> list:
    """Detect potential red flags in candidate profile."""
    red_flags = []
    
    # Check for frequent job changes
    experience = candidate_data.get("experience", [])
    if len(experience) > 4:  # More than 4 jobs might indicate job hopping
        red_flags.append("Frequent job changes - potential job hopping pattern")
    
    # Check for employment gaps (simplified check)
    if len(experience) < candidate_data.get("total_experience_years", 0) / 2:
        red_flags.append("Potential employment gaps detected")
    
    # Check for missing contact information
    if not candidate_data.get("phone") or not candidate_data.get("email"):
        red_flags.append("Incomplete contact information")
    
    return red_flags


def calculate_compatibility_score(screening_results: dict) -> float:
    """Calculate overall compatibility score from screening results."""
    weights = {
        "skills_match": 0.35,
        "experience_match": 0.25,
        "education_match": 0.15,
        "location_match": 0.15,
        "salary_match": 0.10
    }
    
    total_score = 0
    for category, weight in weights.items():
        if category in screening_results:
            total_score += screening_results[category]["score"] * weight
    
    # Deduct points for red flags
    red_flag_penalty = len(screening_results.get("red_flags", [])) * 5
    total_score = max(0, total_score - red_flag_penalty)
    
    return round(total_score, 1)


def determine_screening_decision(score: float, screening_results: dict) -> str:
    """Determine screening decision based on score and results."""
    red_flags = screening_results.get("red_flags", [])
    
    if score >= 80 and len(red_flags) == 0:
        return "strong_candidate"
    elif score >= 65 and len(red_flags) <= 1:
        return "proceed"
    elif score >= 50:
        return "conditional"
    else:
        return "reject"


def get_next_steps(decision: str, screening_results: dict) -> list:
    """Get recommended next steps based on screening decision."""
    if decision == "strong_candidate":
        return [
            "Schedule technical interview immediately",
            "Prepare customized interview questions",
            "Fast-track through hiring process"
        ]
    elif decision == "proceed":
        return [
            "Schedule initial phone screening",
            "Verify key skills and experience",
            "Check references if proceeding"
        ]
    elif decision == "conditional":
        return [
            "Conduct additional skills assessment",
            "Address concerns about missing requirements",
            "Consider for junior or alternative roles"
        ]
    else:
        return [
            "Send polite rejection email",
            "Provide constructive feedback",
            "Keep in talent pool for future opportunities"
        ]


def determine_interview_panel(interview_type: str, candidate_role: str) -> list:
    """Determine appropriate interview panel based on type and role."""
    if interview_type == "technical":
        return ["Senior Engineer", "Tech Lead", "Engineering Manager"]
    elif interview_type == "behavioral":
        return ["HR Manager", "Hiring Manager", "Team Lead"]
    elif interview_type == "final":
        return ["Department Director", "HR Director", "CEO"]
    else:
        return ["Hiring Manager", "HR Representative"]


def find_available_interview_slots(panel: list, preferences: dict) -> list:
    """Find available interview time slots for the panel."""
    # Simulate calendar availability
    base_time = datetime.now() + timedelta(days=2)
    
    return [
        {
            "datetime": (base_time + timedelta(hours=10)).isoformat(),
            "duration": 60,
            "location": "Conference Room A",
            "available_panel": panel
        },
        {
            "datetime": (base_time + timedelta(days=1, hours=14)).isoformat(),
            "duration": 45,
            "location": "Video Conference",
            "available_panel": panel
        }
    ]


def send_interview_notifications(interview_id: str, candidate_data: dict, panel: list, slot: dict) -> list:
    """Send interview notifications to candidate and panel."""
    notifications = []
    
    if slot:
        # Candidate notification
        notifications.append({
            "type": "candidate_email",
            "recipient": candidate_data["email"],
            "subject": "Interview Scheduled",
            "message": f"Your interview has been scheduled for {slot['datetime']}",
            "sent_at": datetime.now().isoformat()
        })
        
        # Panel notifications
        for interviewer in panel:
            notifications.append({
                "type": "interviewer_calendar",
                "recipient": interviewer,
                "subject": f"Interview: {candidate_data['name']}",
                "message": f"Interview scheduled for {slot['datetime']}",
                "sent_at": datetime.now().isoformat()
            })
    
    return notifications


# Create the HR recruitment agent
root_agent = Agent(
    name="recruitment_agent",
    model="gemini-2.0-flash",
    description="Production-ready automated recruitment system with resume parsing, candidate screening, and interview scheduling",
    instruction="""
    You are a sophisticated HR recruitment agent that streamlines the hiring process through intelligent automation.

    Current Candidate Data: {parsed_resumes}
    Screening Results: {candidate_screenings}
    Scheduled Interviews: {scheduled_interviews}

    Your capabilities include:
    1. Advanced resume parsing and candidate data extraction
    2. Comprehensive candidate screening against job requirements
    3. Automated interview scheduling and coordination
    4. Skills matching and compatibility scoring

    Recruitment Process:
    1. Resume Parsing: Extract structured data from candidate resumes
    2. Candidate Screening: Evaluate against job requirements and score compatibility
    3. Interview Scheduling: Coordinate interviews for qualified candidates
    4. Decision Support: Provide recommendations based on screening results

    Screening Criteria:
    - Skills Match (35%): Required vs. candidate skills alignment
    - Experience Match (25%): Years and relevance of experience
    - Education Match (15%): Degree requirements and qualifications
    - Location Match (15%): Geographic compatibility or remote work
    - Salary Match (10%): Expectation vs. budget alignment

    Decision Framework:
    - Strong Candidate (80+ score, no red flags): Fast-track hiring
    - Proceed (65-79 score, ≤1 red flag): Standard interview process
    - Conditional (50-64 score): Additional assessment required
    - Reject (<50 score): Not suitable for current role

    When processing candidates:
    1. Parse resumes thoroughly and extract all relevant information
    2. Screen against specific job requirements with detailed scoring
    3. Identify red flags and areas of concern
    4. Provide clear recommendations and next steps
    5. Schedule interviews efficiently for qualified candidates

    Always maintain:
    - Bias-free evaluation based on objective criteria
    - Consistent screening standards across all candidates
    - Clear documentation of decisions for compliance
    - Respectful communication with all candidates
    - Efficient process flow to reduce time-to-hire

    Focus on finding the best talent while ensuring a positive candidate experience throughout the recruitment process.
    """,
    tools=[parse_resume, screen_candidate, schedule_interview]
)
