from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext
from datetime import datetime, timedelta
import uuid
import json
import random


def create_social_content(brand_profile: str, content_type: str, topic: str, target_platforms: str, tool_context: ToolContext) -> dict:
    """Create engaging social media content based on brand profile and trending topics."""
    print(f"--- Tool: create_social_content called for {content_type} about {topic} ---")
    
    try:
        brand_info = json.loads(brand_profile)
        platforms = json.loads(target_platforms) if target_platforms else ["twitter", "facebook", "linkedin"]
    except json.JSONDecodeError:
        return {
            "action": "create_social_content",
            "error": "Invalid brand profile or platform data format",
            "status": "error"
        }
    
    # Generate content based on brand voice and topic
    content_variations = generate_content_variations(brand_info, content_type, topic, platforms)
    
    # Optimize content for each platform
    platform_optimized_content = {}
    for platform in platforms:
        platform_optimized_content[platform] = optimize_content_for_platform(
            content_variations["base_content"], platform, brand_info
        )
    
    # Generate hashtags and mentions
    hashtags = generate_relevant_hashtags(topic, brand_info, platforms)
    mentions = suggest_relevant_mentions(topic, brand_info)
    
    # Determine optimal posting times
    optimal_times = calculate_optimal_posting_times(platforms, brand_info.get("target_audience", {}))
    
    # Store content creation
    content_id = f"CONT-{str(uuid.uuid4())[:8].upper()}"
    social_content = tool_context.state.get("social_content", {})
    social_content[content_id] = {
        "content_id": content_id,
        "brand_id": brand_info.get("brand_id", "unknown"),
        "created_at": datetime.now().isoformat(),
        "content_type": content_type,
        "topic": topic,
        "target_platforms": platforms,
        "base_content": content_variations["base_content"],
        "platform_content": platform_optimized_content,
        "hashtags": hashtags,
        "mentions": mentions,
        "optimal_times": optimal_times,
        "status": "draft"
    }
    tool_context.state["social_content"] = social_content
    
    return {
        "action": "create_social_content",
        "content_id": content_id,
        "content_type": content_type,
        "topic": topic,
        "base_content": content_variations["base_content"],
        "platform_variations": platform_optimized_content,
        "recommended_hashtags": hashtags[:5],  # Top 5 hashtags
        "optimal_posting_times": optimal_times,
        "estimated_reach": estimate_content_reach(platforms, hashtags, brand_info),
        "status": "success"
    }


def schedule_social_posts(content_id: str, scheduling_strategy: str, tool_context: ToolContext) -> dict:
    """Schedule social media posts across multiple platforms with optimal timing."""
    print(f"--- Tool: schedule_social_posts called for content {content_id} ---")
    
    social_content = tool_context.state.get("social_content", {})
    
    if content_id not in social_content:
        return {
            "action": "schedule_social_posts",
            "error": f"Content {content_id} not found",
            "status": "error"
        }
    
    content = social_content[content_id]
    
    # Determine scheduling strategy
    if scheduling_strategy == "immediate":
        schedule_times = {platform: datetime.now() for platform in content["target_platforms"]}
    elif scheduling_strategy == "optimal":
        schedule_times = {}
        for platform in content["target_platforms"]:
            optimal_time = content["optimal_times"].get(platform, {}).get("next_optimal")
            if optimal_time:
                schedule_times[platform] = datetime.fromisoformat(optimal_time)
            else:
                schedule_times[platform] = datetime.now() + timedelta(hours=1)
    elif scheduling_strategy == "staggered":
        base_time = datetime.now() + timedelta(hours=1)
        schedule_times = {}
        for i, platform in enumerate(content["target_platforms"]):
            schedule_times[platform] = base_time + timedelta(minutes=i * 15)
    else:
        return {
            "action": "schedule_social_posts",
            "error": f"Unknown scheduling strategy: {scheduling_strategy}",
            "status": "error"
        }
    
    # Create scheduled posts
    scheduled_posts = []
    for platform, schedule_time in schedule_times.items():
        post_id = f"POST-{str(uuid.uuid4())[:8].upper()}"
        
        scheduled_post = {
            "post_id": post_id,
            "content_id": content_id,
            "platform": platform,
            "content": content["platform_content"][platform]["text"],
            "hashtags": content["hashtags"],
            "mentions": content["mentions"],
            "scheduled_time": schedule_time.isoformat(),
            "status": "scheduled",
            "created_at": datetime.now().isoformat()
        }
        
        scheduled_posts.append(scheduled_post)
    
    # Store scheduled posts
    social_posts = tool_context.state.get("social_posts", {})
    for post in scheduled_posts:
        social_posts[post["post_id"]] = post
    tool_context.state["social_posts"] = social_posts
    
    # Update content status
    content["status"] = "scheduled"
    content["scheduled_posts"] = [post["post_id"] for post in scheduled_posts]
    social_content[content_id] = content
    tool_context.state["social_content"] = social_content
    
    return {
        "action": "schedule_social_posts",
        "content_id": content_id,
        "scheduling_strategy": scheduling_strategy,
        "scheduled_posts": len(scheduled_posts),
        "platforms": list(schedule_times.keys()),
        "earliest_post": min(schedule_times.values()).isoformat(),
        "latest_post": max(schedule_times.values()).isoformat(),
        "post_details": [
            {
                "platform": post["platform"],
                "scheduled_time": post["scheduled_time"],
                "post_id": post["post_id"]
            }
            for post in scheduled_posts
        ],
        "status": "success"
    }


def monitor_social_engagement(brand_id: str, monitoring_period_hours: int, tool_context: ToolContext) -> dict:
    """Monitor social media engagement, mentions, and sentiment across platforms."""
    print(f"--- Tool: monitor_social_engagement called for brand {brand_id} ---")
    
    # Get recent posts for the brand
    social_posts = tool_context.state.get("social_posts", {})
    social_content = tool_context.state.get("social_content", {})
    
    # Filter posts for this brand within monitoring period
    cutoff_time = datetime.now() - timedelta(hours=monitoring_period_hours)
    brand_posts = []
    
    for post in social_posts.values():
        content = social_content.get(post["content_id"], {})
        if (content.get("brand_id") == brand_id and 
            datetime.fromisoformat(post["created_at"]) > cutoff_time):
            brand_posts.append(post)
    
    # Simulate engagement monitoring
    engagement_data = simulate_engagement_monitoring(brand_posts, monitoring_period_hours)
    
    # Analyze sentiment
    sentiment_analysis = analyze_brand_sentiment(brand_id, monitoring_period_hours)
    
    # Detect trending mentions
    trending_mentions = detect_trending_mentions(brand_id, monitoring_period_hours)
    
    # Identify engagement opportunities
    engagement_opportunities = identify_engagement_opportunities(engagement_data, sentiment_analysis)
    
    # Generate alerts if necessary
    alerts = generate_engagement_alerts(engagement_data, sentiment_analysis, trending_mentions)
    
    # Store monitoring results
    monitoring_id = f"MON-{str(uuid.uuid4())[:8].upper()}"
    engagement_monitoring = tool_context.state.get("engagement_monitoring", {})
    engagement_monitoring[monitoring_id] = {
        "monitoring_id": monitoring_id,
        "brand_id": brand_id,
        "monitored_at": datetime.now().isoformat(),
        "monitoring_period_hours": monitoring_period_hours,
        "posts_analyzed": len(brand_posts),
        "engagement_data": engagement_data,
        "sentiment_analysis": sentiment_analysis,
        "trending_mentions": trending_mentions,
        "engagement_opportunities": engagement_opportunities,
        "alerts": alerts
    }
    tool_context.state["engagement_monitoring"] = engagement_monitoring
    
    return {
        "action": "monitor_social_engagement",
        "monitoring_id": monitoring_id,
        "brand_id": brand_id,
        "monitoring_period_hours": monitoring_period_hours,
        "total_engagement": engagement_data["total_engagement"],
        "engagement_rate": engagement_data["engagement_rate"],
        "sentiment_score": sentiment_analysis["overall_sentiment_score"],
        "trending_mentions": len(trending_mentions),
        "critical_alerts": [alert for alert in alerts if alert["severity"] == "critical"],
        "engagement_opportunities": len(engagement_opportunities),
        "top_performing_post": engagement_data.get("top_performing_post"),
        "status": "success"
    }


def generate_content_variations(brand_info: dict, content_type: str, topic: str, platforms: list) -> dict:
    """Generate content variations based on brand voice and topic."""
    brand_voice = brand_info.get("brand_voice", "professional")
    industry = brand_info.get("industry", "technology")
    
    # Base content templates by type and voice
    if content_type == "promotional":
        if brand_voice == "casual":
            base_content = f"🚀 Exciting news about {topic}! We're thrilled to share this with our amazing community. What do you think?"
        elif brand_voice == "professional":
            base_content = f"We're pleased to announce developments in {topic}. This represents a significant step forward in our commitment to innovation."
        else:  # friendly
            base_content = f"Great news, everyone! We've got some fantastic updates about {topic} to share with you today!"
    
    elif content_type == "educational":
        if brand_voice == "casual":
            base_content = f"Let's dive into {topic}! Here's what you need to know (and why it matters) 🧠"
        elif brand_voice == "professional":
            base_content = f"Understanding {topic}: Key insights and implications for {industry} professionals."
        else:  # friendly
            base_content = f"Learning time! Here's everything you should know about {topic} - made simple and easy to understand."
    
    elif content_type == "engagement":
        if brand_voice == "casual":
            base_content = f"Quick question for our community: What's your take on {topic}? Drop your thoughts below! 👇"
        elif brand_voice == "professional":
            base_content = f"We'd value your perspective on {topic}. How do you see this impacting our industry?"
        else:  # friendly
            base_content = f"We'd love to hear from you! What are your thoughts on {topic}? Share your experience!"
    
    else:  # general
        base_content = f"Sharing some thoughts on {topic}. What's your experience been like?"
    
    return {
        "base_content": base_content,
        "content_type": content_type,
        "brand_voice": brand_voice
    }


def optimize_content_for_platform(base_content: str, platform: str, brand_info: dict) -> dict:
    """Optimize content for specific social media platform."""
    if platform == "twitter":
        # Twitter optimization: shorter, hashtag-friendly
        optimized_text = base_content[:240]  # Leave room for hashtags
        character_limit = 280
        optimal_length = "short"
    
    elif platform == "linkedin":
        # LinkedIn optimization: professional tone, longer form
        optimized_text = base_content + "\n\nWhat are your thoughts on this? I'd love to hear your perspective in the comments."
        character_limit = 3000
        optimal_length = "medium"
    
    elif platform == "facebook":
        # Facebook optimization: engaging, community-focused
        optimized_text = base_content + "\n\nTag a friend who would find this interesting!"
        character_limit = 63206
        optimal_length = "medium"
    
    elif platform == "instagram":
        # Instagram optimization: visual-first, story-driven
        optimized_text = base_content + "\n\n📸 Swipe to see more!"
        character_limit = 2200
        optimal_length = "short"
    
    else:
        optimized_text = base_content
        character_limit = 1000
        optimal_length = "medium"
    
    return {
        "text": optimized_text,
        "character_count": len(optimized_text),
        "character_limit": character_limit,
        "optimal_length": optimal_length,
        "platform_specific_features": get_platform_features(platform)
    }


def generate_relevant_hashtags(topic: str, brand_info: dict, platforms: list) -> list:
    """Generate relevant hashtags for the content."""
    industry = brand_info.get("industry", "technology")
    
    # Base hashtags by topic and industry
    topic_hashtags = {
        "ai": ["#AI", "#ArtificialIntelligence", "#MachineLearning", "#Innovation", "#Tech"],
        "marketing": ["#Marketing", "#DigitalMarketing", "#SocialMedia", "#Branding", "#Growth"],
        "business": ["#Business", "#Entrepreneurship", "#Leadership", "#Strategy", "#Success"],
        "technology": ["#Technology", "#Innovation", "#Digital", "#Future", "#TechTrends"]
    }
    
    industry_hashtags = {
        "technology": ["#TechInnovation", "#SoftwareDevelopment", "#DigitalTransformation"],
        "healthcare": ["#Healthcare", "#MedTech", "#HealthInnovation"],
        "finance": ["#FinTech", "#Finance", "#Banking", "#Investment"],
        "retail": ["#Retail", "#Ecommerce", "#CustomerExperience"]
    }
    
    # Combine relevant hashtags
    hashtags = []
    
    # Add topic-specific hashtags
    for key, tags in topic_hashtags.items():
        if key.lower() in topic.lower():
            hashtags.extend(tags[:3])
    
    # Add industry hashtags
    if industry in industry_hashtags:
        hashtags.extend(industry_hashtags[industry][:2])
    
    # Add trending hashtags (simulated)
    trending = ["#MondayMotivation", "#TechTuesday", "#WisdomWednesday", "#ThrowbackThursday", "#FeatureFriday"]
    day_of_week = datetime.now().weekday()
    if day_of_week < len(trending):
        hashtags.append(trending[day_of_week])
    
    return list(set(hashtags))  # Remove duplicates


def suggest_relevant_mentions(topic: str, brand_info: dict) -> list:
    """Suggest relevant mentions for the content."""
    # Simulate relevant mentions based on topic and industry
    mentions = []
    
    if "technology" in topic.lower():
        mentions.extend(["@TechCrunch", "@Wired", "@TheVerge"])
    
    if "business" in topic.lower():
        mentions.extend(["@Forbes", "@HarvardBiz", "@Inc"])
    
    # Add industry influencers (simulated)
    industry = brand_info.get("industry", "")
    if industry == "technology":
        mentions.extend(["@sundarpichai", "@satyanadella", "@tim_cook"])
    
    return mentions[:3]  # Limit to top 3 mentions


def calculate_optimal_posting_times(platforms: list, target_audience: dict) -> dict:
    """Calculate optimal posting times for each platform."""
    # Simulate optimal posting times based on platform and audience
    base_times = {
        "twitter": {"hour": 12, "minute": 0},  # Noon
        "facebook": {"hour": 15, "minute": 0},  # 3 PM
        "linkedin": {"hour": 8, "minute": 30},  # 8:30 AM
        "instagram": {"hour": 17, "minute": 0}  # 5 PM
    }
    
    optimal_times = {}
    
    for platform in platforms:
        if platform in base_times:
            base_time = base_times[platform]
            
            # Calculate next optimal time
            now = datetime.now()
            next_optimal = now.replace(
                hour=base_time["hour"],
                minute=base_time["minute"],
                second=0,
                microsecond=0
            )
            
            # If time has passed today, schedule for tomorrow
            if next_optimal <= now:
                next_optimal += timedelta(days=1)
            
            optimal_times[platform] = {
                "next_optimal": next_optimal.isoformat(),
                "recommended_days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
                "peak_engagement_hour": base_time["hour"]
            }
    
    return optimal_times


def estimate_content_reach(platforms: list, hashtags: list, brand_info: dict) -> dict:
    """Estimate potential reach for the content."""
    # Simulate reach estimation
    base_reach = {
        "twitter": 1000,
        "facebook": 2000,
        "linkedin": 500,
        "instagram": 1500
    }
    
    total_estimated_reach = 0
    platform_reach = {}
    
    for platform in platforms:
        platform_base = base_reach.get(platform, 1000)
        
        # Boost for hashtags
        hashtag_boost = len(hashtags) * 0.1
        
        # Boost for brand followers (simulated)
        follower_boost = brand_info.get("follower_count", 1000) * 0.05
        
        estimated_reach = int(platform_base * (1 + hashtag_boost) + follower_boost)
        platform_reach[platform] = estimated_reach
        total_estimated_reach += estimated_reach
    
    return {
        "total_estimated_reach": total_estimated_reach,
        "platform_breakdown": platform_reach,
        "engagement_rate_estimate": "3-5%"
    }


def get_platform_features(platform: str) -> list:
    """Get platform-specific features and recommendations."""
    features = {
        "twitter": ["Use threads for longer content", "Include relevant hashtags", "Engage with replies quickly"],
        "facebook": ["Add call-to-action buttons", "Use Facebook Groups", "Share to relevant communities"],
        "linkedin": ["Tag relevant professionals", "Share in industry groups", "Use LinkedIn polls"],
        "instagram": ["Add Instagram Stories", "Use Instagram Reels", "Include location tags"]
    }

    return features.get(platform, ["Optimize for platform best practices"])


def simulate_engagement_monitoring(brand_posts: list, monitoring_period_hours: int) -> dict:
    """Simulate engagement monitoring for brand posts."""
    total_likes = sum(random.randint(10, 500) for _ in brand_posts)
    total_comments = sum(random.randint(2, 50) for _ in brand_posts)
    total_shares = sum(random.randint(1, 25) for _ in brand_posts)
    total_engagement = total_likes + total_comments + total_shares

    # Calculate engagement rate
    estimated_reach = len(brand_posts) * 1000  # Assume 1000 reach per post
    engagement_rate = (total_engagement / estimated_reach) * 100 if estimated_reach > 0 else 0

    # Find top performing post
    top_performing_post = None
    if brand_posts:
        top_performing_post = {
            "post_id": brand_posts[0].get("post_id", "unknown"),
            "engagement_score": random.randint(50, 200),
            "platform": brand_posts[0].get("platform", "unknown")
        }

    return {
        "total_engagement": total_engagement,
        "total_likes": total_likes,
        "total_comments": total_comments,
        "total_shares": total_shares,
        "engagement_rate": round(engagement_rate, 2),
        "estimated_reach": estimated_reach,
        "top_performing_post": top_performing_post
    }


def analyze_brand_sentiment(brand_id: str, monitoring_period_hours: int) -> dict:
    """Analyze brand sentiment from social media mentions."""
    # Simulate sentiment analysis
    positive_mentions = random.randint(20, 80)
    negative_mentions = random.randint(5, 30)
    neutral_mentions = random.randint(10, 40)
    total_mentions = positive_mentions + negative_mentions + neutral_mentions

    # Calculate sentiment score (-100 to +100)
    sentiment_score = ((positive_mentions - negative_mentions) / total_mentions) * 100 if total_mentions > 0 else 0

    return {
        "overall_sentiment_score": round(sentiment_score, 1),
        "positive_mentions": positive_mentions,
        "negative_mentions": negative_mentions,
        "neutral_mentions": neutral_mentions,
        "total_mentions": total_mentions,
        "sentiment_trend": "improving" if sentiment_score > 20 else "declining" if sentiment_score < -20 else "stable"
    }


def detect_trending_mentions(brand_id: str, monitoring_period_hours: int) -> list:
    """Detect trending mentions and hashtags."""
    # Simulate trending mentions
    trending_topics = [
        {"topic": "#TechInnovation", "mentions": 45, "growth_rate": 120},
        {"topic": "@brand_mention", "mentions": 23, "growth_rate": 85},
        {"topic": "#CustomerService", "mentions": 18, "growth_rate": 200}
    ]

    return trending_topics


def identify_engagement_opportunities(engagement_data: dict, sentiment_analysis: dict) -> list:
    """Identify opportunities for increased engagement."""
    opportunities = []

    if engagement_data["engagement_rate"] < 3.0:
        opportunities.append({
            "type": "low_engagement",
            "recommendation": "Increase posting frequency and use more engaging content formats",
            "priority": "medium"
        })

    if sentiment_analysis["negative_mentions"] > 10:
        opportunities.append({
            "type": "reputation_management",
            "recommendation": "Address negative feedback and improve customer service responses",
            "priority": "high"
        })

    if engagement_data["total_shares"] < engagement_data["total_likes"] * 0.1:
        opportunities.append({
            "type": "shareability",
            "recommendation": "Create more shareable content with strong calls-to-action",
            "priority": "medium"
        })

    return opportunities


def generate_engagement_alerts(engagement_data: dict, sentiment_analysis: dict, trending_mentions: list) -> list:
    """Generate alerts based on engagement monitoring."""
    alerts = []

    # Low engagement alert
    if engagement_data["engagement_rate"] < 1.0:
        alerts.append({
            "type": "low_engagement",
            "severity": "warning",
            "message": f"Engagement rate dropped to {engagement_data['engagement_rate']}%",
            "recommended_action": "Review content strategy and posting schedule"
        })

    # Negative sentiment alert
    if sentiment_analysis["overall_sentiment_score"] < -30:
        alerts.append({
            "type": "negative_sentiment",
            "severity": "critical",
            "message": f"Brand sentiment is negative: {sentiment_analysis['overall_sentiment_score']}",
            "recommended_action": "Immediate reputation management required"
        })

    # Viral content opportunity
    if any(mention["growth_rate"] > 150 for mention in trending_mentions):
        alerts.append({
            "type": "viral_opportunity",
            "severity": "info",
            "message": "Trending topic detected with high growth rate",
            "recommended_action": "Create content around trending topic"
        })

    return alerts


# Create the social media manager agent
root_agent = Agent(
    name="social_media_agent",
    model="gemini-2.0-flash",
    description="Production-ready social media management system with content creation, scheduling, and engagement monitoring",
    instruction="""
    You are a sophisticated social media manager agent that automates content creation, scheduling, and engagement across multiple social platforms.

    Current Content: {social_content}
    Scheduled Posts: {social_posts}
    Engagement Monitoring: {engagement_monitoring}

    Your capabilities include:
    1. AI-powered content creation optimized for different platforms and brand voices
    2. Intelligent scheduling based on optimal posting times and audience behavior
    3. Real-time engagement monitoring with sentiment analysis and trend detection
    4. Performance analytics and optimization recommendations

    Social Media Management Process:
    1. Create engaging content tailored to brand voice and target audience
    2. Optimize content for each platform's unique characteristics and algorithms
    3. Schedule posts at optimal times for maximum engagement
    4. Monitor engagement, mentions, and sentiment in real-time

    Content Creation Guidelines:
    - Match brand voice (professional, casual, friendly) consistently
    - Optimize content length and format for each platform
    - Include relevant hashtags and mentions for increased reach
    - Create platform-specific variations while maintaining core message

    Platform Optimization:
    - Twitter: Concise, hashtag-rich, real-time engagement
    - LinkedIn: Professional tone, industry insights, thought leadership
    - Facebook: Community-focused, longer-form content, visual elements
    - Instagram: Visual-first, story-driven, lifestyle content

    When creating content:
    1. Analyze brand voice and target audience preferences
    2. Research trending topics and relevant hashtags
    3. Create platform-specific variations of core content
    4. Suggest optimal posting times based on audience behavior
    5. Estimate reach and engagement potential

    Engagement Monitoring:
    - Track mentions, comments, and direct messages across platforms
    - Analyze sentiment and detect potential crisis situations
    - Identify trending topics and engagement opportunities
    - Generate alerts for critical issues requiring immediate attention

    Focus on:
    - Maintaining consistent brand voice across all platforms
    - Maximizing engagement through optimal timing and content
    - Building authentic relationships with the audience
    - Driving measurable business results through social media
    - Staying current with platform algorithm changes and best practices
    """,
    tools=[create_social_content, schedule_social_posts, monitor_social_engagement]
)
