# Gemini Model Overview for ADK

ADK supports several Gemini models with different capabilities and price points. Choosing the right model involves balancing performance, capabilities, and cost for your specific use case.

## Model Capabilities

| Model | Description | Input Types | Best For |
|-------|-------------|-------------|----------|
| gemini-2.5-pro | Most powerful thinking model with maximum response accuracy | Audio, images, video, text | Complex coding, reasoning, multimodal understanding |
| gemini-2.5-flash | Best price-performance balance | Audio, images, video, text | Low latency, high volume tasks that require thinking |
| gemini-2.0-flash | Newest multimodal model with improved capabilities | Audio, images, video, text | Low latency, enhanced performance, agentic experiences |
| gemini-2.0-flash-lite | Optimized for efficiency and speed | Audio, images, video, text | Cost efficiency and low latency |
| gemini-1.5-flash | Versatile performance across diverse tasks | Audio, images, video, text | Fast and versatile performance |
| gemini-1.5-flash-8b | Smaller, faster model | Audio, images, video, text | High volume and lower intelligence tasks |
| gemini-1.5-pro | Powerful reasoning capabilities | Audio, images, video, text | Complex reasoning tasks requiring more intelligence |

## Pricing

| Model | Input Price | Output Price |
|-------|-------------|-------------|
| gemini-2.5-pro | $10.00 / 1M tokens | $30.00 / 1M tokens |
| gemini-2.5-flash | $3.50 / 1M tokens | $10.50 / 1M tokens |
| gemini-2.0-flash | $3.50 / 1M tokens | $10.50 / 1M tokens |
| gemini-2.0-flash-lite | $0.70 / 1M tokens | $2.10 / 1M tokens |
| gemini-1.5-flash | $2.50 / 1M tokens | $7.50 / 1M tokens |
| gemini-1.5-flash-8b | $0.35 / 1M tokens | $1.05 / 1M tokens |
| gemini-1.5-pro | $7.00 / 1M tokens | $21.00 / 1M tokens |

## Token Information

- A token is approximately 4 characters
- 100 tokens are roughly 60-80 English words
- Pricing is calculated based on both input tokens (prompts sent to the model) and output tokens (responses generated by the model)

## Model Selection Guidelines

1. **For budget-conscious applications:** Start with gemini-2.0-flash-lite
2. **For balanced performance and cost:** Use gemini-2.0-flash or gemini-2.5-flash
3. **For complex reasoning tasks:** Choose gemini-2.5-pro
4. **For production applications:** Prefer stable models over experimental/preview versions

## Additional Resources

For the most up-to-date information on Gemini models, visit the [official Gemini API documentation](https://ai.google.dev/gemini-api/docs/models).
