from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext
import json
import hashlib
import hmac
from datetime import datetime
from typing import Dict, Any


def register_webhook_endpoint(service_name: str, endpoint_url: str, secret_key: str, tool_context: ToolContext) -> dict:
    """Register a new webhook endpoint for a service.
    
    Args:
        service_name: Name of the service (e.g., 'github', 'stripe', 'slack')
        endpoint_url: The webhook URL endpoint
        secret_key: Secret key for webhook validation
        tool_context: Context for accessing session state
        
    Returns:
        A dictionary with registration confirmation
    """
    print(f"--- Tool: register_webhook_endpoint called for {service_name} ---")
    
    # Get existing webhooks from state
    webhooks = tool_context.state.get("registered_webhooks", {})
    
    # Register the new webhook
    webhooks[service_name] = {
        "endpoint_url": endpoint_url,
        "secret_key": secret_key,
        "registered_at": datetime.now().isoformat(),
        "status": "active",
        "events_received": 0
    }
    
    # Update state
    tool_context.state["registered_webhooks"] = webhooks
    
    return {
        "action": "register_webhook_endpoint",
        "service_name": service_name,
        "endpoint_url": endpoint_url,
        "message": f"Webhook endpoint registered for {service_name}",
        "webhook_id": f"{service_name}_{hash(endpoint_url) % 10000}"
    }


def process_webhook_payload(service_name: str, payload: str, signature: str, tool_context: ToolContext) -> dict:
    """Process an incoming webhook payload.
    
    Args:
        service_name: Name of the service sending the webhook
        payload: JSON payload from the webhook
        signature: Webhook signature for validation
        tool_context: Context for accessing session state
        
    Returns:
        A dictionary with processing results
    """
    print(f"--- Tool: process_webhook_payload called for {service_name} ---")
    
    # Get registered webhooks
    webhooks = tool_context.state.get("registered_webhooks", {})
    
    if service_name not in webhooks:
        return {
            "action": "process_webhook_payload",
            "error": f"No registered webhook found for service: {service_name}",
            "status": "error"
        }
    
    webhook_config = webhooks[service_name]
    
    # Validate webhook signature
    if not validate_webhook_signature(payload, signature, webhook_config["secret_key"]):
        return {
            "action": "process_webhook_payload",
            "error": "Invalid webhook signature",
            "status": "unauthorized"
        }
    
    try:
        # Parse payload
        parsed_payload = json.loads(payload)
        
        # Process based on service type
        result = process_service_event(service_name, parsed_payload, tool_context)
        
        # Update webhook statistics
        webhook_config["events_received"] += 1
        webhook_config["last_event_at"] = datetime.now().isoformat()
        webhooks[service_name] = webhook_config
        tool_context.state["registered_webhooks"] = webhooks
        
        return {
            "action": "process_webhook_payload",
            "service_name": service_name,
            "event_type": result.get("event_type", "unknown"),
            "processed_at": datetime.now().isoformat(),
            "result": result,
            "status": "success"
        }
        
    except json.JSONDecodeError:
        return {
            "action": "process_webhook_payload",
            "error": "Invalid JSON payload",
            "status": "error"
        }
    except Exception as e:
        return {
            "action": "process_webhook_payload",
            "error": f"Processing error: {str(e)}",
            "status": "error"
        }


def validate_webhook_signature(payload: str, signature: str, secret_key: str) -> bool:
    """Validate webhook signature for security.
    
    Args:
        payload: The webhook payload
        signature: The provided signature
        secret_key: The secret key for validation
        
    Returns:
        Boolean indicating if signature is valid
    """
    # Create expected signature
    expected_signature = hmac.new(
        secret_key.encode('utf-8'),
        payload.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    # Compare signatures securely
    return hmac.compare_digest(f"sha256={expected_signature}", signature)


def process_service_event(service_name: str, payload: Dict[Any, Any], tool_context: ToolContext) -> dict:
    """Process events based on service type.
    
    Args:
        service_name: Name of the service
        payload: Parsed webhook payload
        tool_context: Context for accessing session state
        
    Returns:
        A dictionary with processing results
    """
    if service_name == "github":
        return process_github_event(payload, tool_context)
    elif service_name == "stripe":
        return process_stripe_event(payload, tool_context)
    elif service_name == "slack":
        return process_slack_event(payload, tool_context)
    else:
        return process_generic_event(payload, tool_context)


def process_github_event(payload: Dict[Any, Any], tool_context: ToolContext) -> dict:
    """Process GitHub webhook events."""
    event_type = payload.get("action", "unknown")
    
    if "repository" in payload:
        repo_name = payload["repository"]["name"]
        return {
            "event_type": f"github_{event_type}",
            "repository": repo_name,
            "message": f"GitHub {event_type} event processed for {repo_name}"
        }
    
    return {
        "event_type": f"github_{event_type}",
        "message": f"GitHub {event_type} event processed"
    }


def process_stripe_event(payload: Dict[Any, Any], tool_context: ToolContext) -> dict:
    """Process Stripe webhook events."""
    event_type = payload.get("type", "unknown")
    
    return {
        "event_type": f"stripe_{event_type}",
        "message": f"Stripe {event_type} event processed"
    }


def process_slack_event(payload: Dict[Any, Any], tool_context: ToolContext) -> dict:
    """Process Slack webhook events."""
    event_type = payload.get("type", "unknown")
    
    return {
        "event_type": f"slack_{event_type}",
        "message": f"Slack {event_type} event processed"
    }


def process_generic_event(payload: Dict[Any, Any], tool_context: ToolContext) -> dict:
    """Process generic webhook events."""
    return {
        "event_type": "generic_webhook",
        "message": "Generic webhook event processed",
        "payload_keys": list(payload.keys())
    }


def list_webhook_endpoints(tool_context: ToolContext) -> dict:
    """List all registered webhook endpoints.
    
    Args:
        tool_context: Context for accessing session state
        
    Returns:
        A dictionary with all registered webhooks
    """
    print("--- Tool: list_webhook_endpoints called ---")
    
    webhooks = tool_context.state.get("registered_webhooks", {})
    
    # Remove sensitive information for display
    safe_webhooks = {}
    for service, config in webhooks.items():
        safe_webhooks[service] = {
            "endpoint_url": config["endpoint_url"],
            "registered_at": config["registered_at"],
            "status": config["status"],
            "events_received": config["events_received"],
            "last_event_at": config.get("last_event_at", "Never")
        }
    
    return {
        "action": "list_webhook_endpoints",
        "webhooks": safe_webhooks,
        "total_endpoints": len(webhooks)
    }


# Create the webhook agent
root_agent = Agent(
    name="webhook_agent",
    model="gemini-2.0-flash",
    description="Agent that handles webhooks and integrates with external services for event-driven architectures",
    instruction="""
    You are a webhook agent that specializes in handling HTTP webhooks and integrating with external services.
    
    Your capabilities include:
    1. Registering webhook endpoints for various services
    2. Processing incoming webhook payloads securely
    3. Validating webhook signatures for security
    4. Routing events to appropriate handlers
    5. Managing webhook configurations and statistics
    
    Supported services include:
    - GitHub (push events, pull requests, issues)
    - Stripe (payments, subscriptions)
    - Slack (slash commands, events)
    - Generic HTTP webhooks
    
    When users want to work with webhooks:
    - Use register_webhook_endpoint to set up new webhook endpoints
    - Use process_webhook_payload to handle incoming webhook data
    - Use list_webhook_endpoints to view registered webhooks
    
    Always emphasize security best practices:
    - Signature validation is mandatory
    - Use HTTPS endpoints only
    - Validate all incoming data
    - Log webhook activity for auditing
    
    For demonstrations, you can simulate webhook payloads and show how they would be processed.
    """,
    tools=[register_webhook_endpoint, process_webhook_payload, list_webhook_endpoints]
)
