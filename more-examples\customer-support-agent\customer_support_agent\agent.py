from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext
from datetime import datetime, timedelta
import uuid
import re


def create_support_ticket(customer_email: str, issue_category: str, issue_description: str, priority: str, tool_context: ToolContext) -> dict:
    """Create a new support ticket for the customer."""
    print(f"--- Tool: create_support_ticket called for {customer_email} ---")
    
    # Generate ticket ID
    ticket_id = f"TKT-{str(uuid.uuid4())[:8].upper()}"
    
    # Get customer info
    customer_info = get_customer_info(customer_email, tool_context)
    
    # Determine priority based on customer tier and issue
    calculated_priority = calculate_priority(customer_info, issue_category, priority)
    
    # Store ticket
    tickets = tool_context.state.get("support_tickets", {})
    tickets[ticket_id] = {
        "ticket_id": ticket_id,
        "customer_email": customer_email,
        "customer_tier": customer_info.get("tier", "standard"),
        "issue_category": issue_category,
        "issue_description": issue_description,
        "priority": calculated_priority,
        "status": "open",
        "created_at": datetime.now().isoformat(),
        "last_updated": datetime.now().isoformat(),
        "assigned_agent": None,
        "resolution": None,
        "customer_satisfaction": None
    }
    
    tool_context.state["support_tickets"] = tickets
    
    # Auto-assign based on category and priority
    assignment = auto_assign_ticket(ticket_id, issue_category, calculated_priority, tool_context)
    
    return {
        "action": "create_support_ticket",
        "ticket_id": ticket_id,
        "customer_email": customer_email,
        "priority": calculated_priority,
        "category": issue_category,
        "assignment": assignment,
        "estimated_resolution": get_estimated_resolution_time(calculated_priority, issue_category),
        "message": f"Support ticket {ticket_id} created successfully",
        "status": "success"
    }


def search_knowledge_base(query: str, category: str, tool_context: ToolContext) -> dict:
    """Search the knowledge base for solutions to customer issues."""
    print(f"--- Tool: search_knowledge_base called for query: {query} ---")
    
    # Simulate knowledge base with common support scenarios
    knowledge_base = {
        "login_issues": [
            {
                "title": "Password Reset Instructions",
                "solution": "1. Go to login page 2. Click 'Forgot Password' 3. Enter your email 4. Check email for reset link 5. Create new password",
                "category": "account",
                "confidence": 0.95
            },
            {
                "title": "Account Locked Due to Multiple Failed Attempts",
                "solution": "Your account is temporarily locked for security. Wait 30 minutes or contact support to unlock immediately.",
                "category": "account",
                "confidence": 0.88
            }
        ],
        "billing_issues": [
            {
                "title": "Payment Method Update",
                "solution": "1. Log into your account 2. Go to Billing Settings 3. Click 'Update Payment Method' 4. Enter new card details 5. Save changes",
                "category": "billing",
                "confidence": 0.92
            },
            {
                "title": "Refund Processing Time",
                "solution": "Refunds typically process within 5-7 business days. Credit card refunds may take 1-2 additional days to appear on your statement.",
                "category": "billing",
                "confidence": 0.90
            }
        ],
        "technical_issues": [
            {
                "title": "App Crashes on Startup",
                "solution": "1. Force close the app 2. Restart your device 3. Update the app to latest version 4. Clear app cache if issue persists",
                "category": "technical",
                "confidence": 0.87
            },
            {
                "title": "Slow Performance Issues",
                "solution": "1. Check internet connection 2. Close other apps 3. Clear browser cache 4. Try using incognito/private mode",
                "category": "technical",
                "confidence": 0.85
            }
        ],
        "shipping_issues": [
            {
                "title": "Order Tracking Information",
                "solution": "You can track your order using the tracking number sent to your email. If you can't find it, provide your order number for assistance.",
                "category": "shipping",
                "confidence": 0.93
            },
            {
                "title": "Delayed Delivery",
                "solution": "Delivery delays can occur due to weather, holidays, or high volume. Check tracking for updates or contact carrier directly.",
                "category": "shipping",
                "confidence": 0.89
            }
        ]
    }
    
    # Search for relevant solutions
    query_lower = query.lower()
    relevant_solutions = []
    
    for kb_category, solutions in knowledge_base.items():
        if category.lower() in kb_category or any(word in kb_category for word in query_lower.split()):
            for solution in solutions:
                # Simple keyword matching
                title_match = any(word in solution["title"].lower() for word in query_lower.split())
                solution_match = any(word in solution["solution"].lower() for word in query_lower.split())
                
                if title_match or solution_match:
                    relevant_solutions.append(solution)
    
    # Sort by confidence
    relevant_solutions.sort(key=lambda x: x["confidence"], reverse=True)
    
    return {
        "action": "search_knowledge_base",
        "query": query,
        "category": category,
        "solutions_found": len(relevant_solutions),
        "solutions": relevant_solutions[:3],  # Return top 3 solutions
        "status": "success"
    }


def analyze_customer_sentiment(message: str, tool_context: ToolContext) -> dict:
    """Analyze customer sentiment from their message."""
    print(f"--- Tool: analyze_customer_sentiment called ---")
    
    # Simple sentiment analysis based on keywords
    positive_words = ["thank", "great", "excellent", "happy", "satisfied", "good", "pleased", "wonderful"]
    negative_words = ["angry", "frustrated", "terrible", "awful", "hate", "worst", "horrible", "disgusted", "furious"]
    urgent_words = ["urgent", "immediately", "asap", "emergency", "critical", "now"]
    
    message_lower = message.lower()
    
    positive_score = sum(1 for word in positive_words if word in message_lower)
    negative_score = sum(1 for word in negative_words if word in message_lower)
    urgent_score = sum(1 for word in urgent_words if word in message_lower)
    
    # Determine overall sentiment
    if negative_score > positive_score:
        if negative_score >= 2 or urgent_score >= 1:
            sentiment = "very_negative"
            escalate = True
        else:
            sentiment = "negative"
            escalate = False
    elif positive_score > negative_score:
        sentiment = "positive"
        escalate = False
    else:
        sentiment = "neutral"
        escalate = False
    
    # Check for caps (indicates shouting/anger)
    caps_ratio = sum(1 for c in message if c.isupper()) / len(message) if message else 0
    if caps_ratio > 0.3:
        sentiment = "very_negative"
        escalate = True
    
    return {
        "action": "analyze_customer_sentiment",
        "sentiment": sentiment,
        "confidence": 0.85,
        "escalate_recommended": escalate,
        "sentiment_indicators": {
            "positive_words": positive_score,
            "negative_words": negative_score,
            "urgent_words": urgent_score,
            "caps_ratio": round(caps_ratio, 2)
        },
        "status": "success"
    }


def escalate_to_human(ticket_id: str, escalation_reason: str, priority: str, tool_context: ToolContext) -> dict:
    """Escalate a ticket to a human agent."""
    print(f"--- Tool: escalate_to_human called for ticket {ticket_id} ---")
    
    tickets = tool_context.state.get("support_tickets", {})
    
    if ticket_id not in tickets:
        return {
            "action": "escalate_to_human",
            "error": f"Ticket {ticket_id} not found",
            "status": "error"
        }
    
    ticket = tickets[ticket_id]
    
    # Update ticket with escalation info
    ticket["status"] = "escalated"
    ticket["escalated_at"] = datetime.now().isoformat()
    ticket["escalation_reason"] = escalation_reason
    ticket["priority"] = "high" if priority != "critical" else "critical"
    
    # Assign to appropriate human agent based on category and priority
    agent_assignment = assign_human_agent(ticket["issue_category"], ticket["priority"])
    ticket["assigned_agent"] = agent_assignment["agent_name"]
    
    tickets[ticket_id] = ticket
    tool_context.state["support_tickets"] = tickets
    
    # Create escalation record
    escalations = tool_context.state.get("escalations", [])
    escalations.append({
        "ticket_id": ticket_id,
        "escalated_at": datetime.now().isoformat(),
        "reason": escalation_reason,
        "assigned_to": agent_assignment["agent_name"],
        "expected_response_time": agent_assignment["response_time"]
    })
    tool_context.state["escalations"] = escalations
    
    return {
        "action": "escalate_to_human",
        "ticket_id": ticket_id,
        "assigned_agent": agent_assignment["agent_name"],
        "expected_response_time": agent_assignment["response_time"],
        "escalation_reason": escalation_reason,
        "message": f"Ticket {ticket_id} escalated to {agent_assignment['agent_name']}",
        "status": "success"
    }


def get_customer_info(email: str, tool_context: ToolContext) -> dict:
    """Get customer information from CRM."""
    # Simulate customer database
    customers = {
        "<EMAIL>": {
            "name": "John Doe",
            "tier": "premium",
            "account_status": "active",
            "join_date": "2023-01-15",
            "total_orders": 15,
            "lifetime_value": 2500.00,
            "last_contact": "2024-01-10"
        },
        "<EMAIL>": {
            "name": "Jane Smith",
            "tier": "standard",
            "account_status": "active",
            "join_date": "2023-06-20",
            "total_orders": 3,
            "lifetime_value": 150.00,
            "last_contact": "2023-12-15"
        }
    }
    
    return customers.get(email, {
        "name": "Unknown Customer",
        "tier": "standard",
        "account_status": "unknown",
        "join_date": None,
        "total_orders": 0,
        "lifetime_value": 0.00,
        "last_contact": None
    })


def calculate_priority(customer_info: dict, issue_category: str, requested_priority: str) -> str:
    """Calculate ticket priority based on customer tier and issue type."""
    base_priority = requested_priority.lower()
    
    # Upgrade priority for premium customers
    if customer_info.get("tier") == "premium":
        if base_priority == "low":
            return "medium"
        elif base_priority == "medium":
            return "high"
    
    # Upgrade priority for critical issue categories
    critical_categories = ["billing", "security", "data_loss"]
    if issue_category.lower() in critical_categories:
        if base_priority in ["low", "medium"]:
            return "high"
    
    return base_priority


def auto_assign_ticket(ticket_id: str, category: str, priority: str, tool_context: ToolContext) -> dict:
    """Auto-assign ticket based on category and priority."""
    # Simulate agent availability and specialization
    if priority == "critical":
        return {"assigned_to": "Senior Agent", "queue": "critical", "estimated_response": "15 minutes"}
    elif category.lower() == "technical":
        return {"assigned_to": "Technical Team", "queue": "technical", "estimated_response": "2 hours"}
    elif category.lower() == "billing":
        return {"assigned_to": "Billing Team", "queue": "billing", "estimated_response": "1 hour"}
    else:
        return {"assigned_to": "General Support", "queue": "general", "estimated_response": "4 hours"}


def assign_human_agent(category: str, priority: str) -> dict:
    """Assign human agent for escalated tickets."""
    if priority == "critical":
        return {"agent_name": "Sarah Johnson (Senior Manager)", "response_time": "15 minutes"}
    elif category.lower() == "technical":
        return {"agent_name": "Mike Chen (Technical Lead)", "response_time": "30 minutes"}
    elif category.lower() == "billing":
        return {"agent_name": "Lisa Rodriguez (Billing Specialist)", "response_time": "1 hour"}
    else:
        return {"agent_name": "David Kim (Support Manager)", "response_time": "2 hours"}


def get_estimated_resolution_time(priority: str, category: str) -> str:
    """Get estimated resolution time based on priority and category."""
    if priority == "critical":
        return "2-4 hours"
    elif priority == "high":
        return "4-8 hours"
    elif priority == "medium":
        return "1-2 business days"
    else:
        return "2-3 business days"


def lookup_order_status(order_number: str, customer_email: str, tool_context: ToolContext) -> dict:
    """Look up order status for customer."""
    print(f"--- Tool: lookup_order_status called for order {order_number} ---")
    
    # Simulate order database
    orders = {
        "12345": {
            "order_number": "12345",
            "customer_email": "<EMAIL>",
            "status": "shipped",
            "tracking_number": "1Z999AA1234567890",
            "estimated_delivery": "2024-01-20",
            "items": ["Laptop", "Mouse"],
            "total": 1299.99
        },
        "67890": {
            "order_number": "67890",
            "customer_email": "<EMAIL>",
            "status": "processing",
            "tracking_number": None,
            "estimated_delivery": "2024-01-22",
            "items": ["Book", "Notebook"],
            "total": 29.99
        }
    }
    
    order = orders.get(order_number)
    
    if not order:
        return {
            "action": "lookup_order_status",
            "error": f"Order {order_number} not found",
            "status": "error"
        }
    
    if order["customer_email"] != customer_email:
        return {
            "action": "lookup_order_status",
            "error": "Order not found for this customer",
            "status": "error"
        }
    
    return {
        "action": "lookup_order_status",
        "order": order,
        "status": "success"
    }


# Create the customer support agent
root_agent = Agent(
    name="customer_support_agent",
    model="gemini-2.0-flash",
    description="Production-ready customer support agent with ticket management, knowledge base search, and escalation capabilities",
    instruction="""
    You are a professional customer support agent for a technology company. Your goal is to provide excellent customer service by resolving issues quickly and efficiently.

    Customer Information: {customer_info}
    Active Tickets: {support_tickets}
    Recent Escalations: {escalations}

    Your capabilities include:
    1. Creating and managing support tickets
    2. Searching knowledge base for solutions
    3. Analyzing customer sentiment and emotions
    4. Escalating complex issues to human agents
    5. Looking up order and account information

    Guidelines:
    - Always be professional, empathetic, and helpful
    - Analyze customer sentiment and adjust your tone accordingly
    - Search the knowledge base first before creating tickets
    - Escalate immediately if customer is very frustrated or angry
    - Provide clear, step-by-step solutions
    - Follow up on ticket status and resolution
    - Prioritize premium customers and critical issues

    For angry or frustrated customers:
    - Acknowledge their frustration immediately
    - Apologize for the inconvenience
    - Escalate to human agent if sentiment is very negative
    - Provide immediate solutions when possible

    Always end interactions by asking if there's anything else you can help with.
    """,
    tools=[create_support_ticket, search_knowledge_base, analyze_customer_sentiment, escalate_to_human, lookup_order_status]
)
