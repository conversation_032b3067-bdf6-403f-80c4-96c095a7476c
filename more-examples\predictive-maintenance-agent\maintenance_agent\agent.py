from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext
from datetime import datetime, timedelta
import uuid
import json
import math
import random


def monitor_equipment_health(equipment_id: str, sensor_data: str, tool_context: ToolContext) -> dict:
    """Monitor equipment health using real-time sensor data and generate health scores."""
    print(f"--- Tool: monitor_equipment_health called for equipment {equipment_id} ---")
    
    try:
        sensors = json.loads(sensor_data)
    except json.JSONDecodeError:
        return {
            "action": "monitor_equipment_health",
            "error": "Invalid sensor data format",
            "status": "error"
        }
    
    # Get equipment baseline data
    equipment_data = get_equipment_baseline(equipment_id, tool_context)
    
    if not equipment_data:
        return {
            "action": "monitor_equipment_health",
            "error": f"Equipment {equipment_id} not found in database",
            "status": "error"
        }
    
    # Analyze sensor readings against baselines
    health_analysis = analyze_sensor_readings(sensors, equipment_data["baselines"])
    
    # Calculate overall health score
    overall_health_score = calculate_health_score(health_analysis)
    
    # Detect anomalies and trends
    anomalies = detect_anomalies(sensors, equipment_data["baselines"])
    trends = analyze_trends(equipment_id, sensors, tool_context)
    
    # Generate alerts if necessary
    alerts = generate_health_alerts(equipment_id, health_analysis, anomalies, overall_health_score)
    
    # Store monitoring data
    monitoring_id = f"MON-{str(uuid.uuid4())[:8].upper()}"
    equipment_monitoring = tool_context.state.get("equipment_monitoring", {})
    equipment_monitoring[monitoring_id] = {
        "monitoring_id": monitoring_id,
        "equipment_id": equipment_id,
        "monitored_at": datetime.now().isoformat(),
        "sensor_readings": sensors,
        "health_analysis": health_analysis,
        "overall_health_score": overall_health_score,
        "anomalies": anomalies,
        "trends": trends,
        "alerts": alerts
    }
    tool_context.state["equipment_monitoring"] = equipment_monitoring
    
    # Update equipment history
    update_equipment_history(equipment_id, overall_health_score, sensors, tool_context)
    
    return {
        "action": "monitor_equipment_health",
        "monitoring_id": monitoring_id,
        "equipment_id": equipment_id,
        "overall_health_score": overall_health_score,
        "health_status": get_health_status(overall_health_score),
        "critical_alerts": [alert for alert in alerts if alert["severity"] == "critical"],
        "anomalies_detected": len(anomalies),
        "trending_parameters": [trend["parameter"] for trend in trends if trend["trend"] == "degrading"],
        "next_monitoring": (datetime.now() + timedelta(hours=1)).isoformat(),
        "status": "success"
    }


def predict_equipment_failure(equipment_id: str, prediction_horizon_days: int, tool_context: ToolContext) -> dict:
    """Predict equipment failure probability and remaining useful life."""
    print(f"--- Tool: predict_equipment_failure called for equipment {equipment_id} ---")
    
    # Get equipment historical data
    equipment_history = get_equipment_history(equipment_id, tool_context)
    
    if not equipment_history:
        return {
            "action": "predict_equipment_failure",
            "error": f"Insufficient historical data for equipment {equipment_id}",
            "status": "error"
        }
    
    # Get equipment specifications and failure modes
    equipment_data = get_equipment_baseline(equipment_id, tool_context)
    failure_modes = equipment_data.get("failure_modes", [])
    
    # Run predictive models
    failure_predictions = {}
    
    for failure_mode in failure_modes:
        prediction = run_failure_prediction_model(equipment_history, failure_mode, prediction_horizon_days)
        failure_predictions[failure_mode["name"]] = prediction
    
    # Calculate overall failure probability
    overall_failure_probability = calculate_overall_failure_probability(failure_predictions)
    
    # Estimate remaining useful life
    remaining_useful_life = estimate_remaining_useful_life(equipment_history, failure_predictions)
    
    # Generate maintenance recommendations
    maintenance_recommendations = generate_maintenance_recommendations(
        equipment_id, failure_predictions, remaining_useful_life, equipment_data
    )
    
    # Store prediction results
    prediction_id = f"PRED-{str(uuid.uuid4())[:8].upper()}"
    failure_predictions_store = tool_context.state.get("failure_predictions", {})
    failure_predictions_store[prediction_id] = {
        "prediction_id": prediction_id,
        "equipment_id": equipment_id,
        "predicted_at": datetime.now().isoformat(),
        "prediction_horizon_days": prediction_horizon_days,
        "failure_predictions": failure_predictions,
        "overall_failure_probability": overall_failure_probability,
        "remaining_useful_life_days": remaining_useful_life,
        "maintenance_recommendations": maintenance_recommendations,
        "confidence_level": calculate_prediction_confidence(equipment_history)
    }
    tool_context.state["failure_predictions"] = failure_predictions_store
    
    return {
        "action": "predict_equipment_failure",
        "prediction_id": prediction_id,
        "equipment_id": equipment_id,
        "prediction_horizon_days": prediction_horizon_days,
        "overall_failure_probability": round(overall_failure_probability * 100, 1),
        "remaining_useful_life_days": remaining_useful_life,
        "highest_risk_failure_mode": max(failure_predictions.items(), key=lambda x: x[1]["probability"])[0],
        "maintenance_recommendations": maintenance_recommendations[:3],  # Top 3 recommendations
        "confidence_level": failure_predictions_store[prediction_id]["confidence_level"],
        "status": "success"
    }


def schedule_predictive_maintenance(equipment_id: str, maintenance_type: str, urgency: str, tool_context: ToolContext) -> dict:
    """Schedule predictive maintenance based on equipment condition and predictions."""
    print(f"--- Tool: schedule_predictive_maintenance called for equipment {equipment_id} ---")
    
    # Get equipment data and current predictions
    equipment_data = get_equipment_baseline(equipment_id, tool_context)
    failure_predictions = tool_context.state.get("failure_predictions", {})
    
    # Find latest prediction for this equipment
    latest_prediction = None
    for prediction in failure_predictions.values():
        if prediction["equipment_id"] == equipment_id:
            if not latest_prediction or prediction["predicted_at"] > latest_prediction["predicted_at"]:
                latest_prediction = prediction
    
    if not latest_prediction:
        return {
            "action": "schedule_predictive_maintenance",
            "error": f"No failure predictions found for equipment {equipment_id}",
            "status": "error"
        }
    
    # Determine optimal maintenance window
    maintenance_window = determine_maintenance_window(
        equipment_id, maintenance_type, urgency, latest_prediction, equipment_data
    )
    
    # Calculate required resources
    resource_requirements = calculate_maintenance_resources(maintenance_type, equipment_data)
    
    # Check resource availability
    resource_availability = check_resource_availability(maintenance_window, resource_requirements, tool_context)
    
    # Generate work order
    work_order = generate_maintenance_work_order(
        equipment_id, maintenance_type, maintenance_window, resource_requirements, latest_prediction
    )
    
    # Store maintenance schedule
    schedule_id = f"MAINT-{str(uuid.uuid4())[:8].upper()}"
    maintenance_schedules = tool_context.state.get("maintenance_schedules", {})
    maintenance_schedules[schedule_id] = {
        "schedule_id": schedule_id,
        "equipment_id": equipment_id,
        "maintenance_type": maintenance_type,
        "urgency": urgency,
        "scheduled_at": datetime.now().isoformat(),
        "maintenance_window": maintenance_window,
        "resource_requirements": resource_requirements,
        "resource_availability": resource_availability,
        "work_order": work_order,
        "prediction_basis": latest_prediction["prediction_id"],
        "status": "scheduled"
    }
    tool_context.state["maintenance_schedules"] = maintenance_schedules
    
    # Send notifications
    notifications = send_maintenance_notifications(schedule_id, work_order, resource_requirements)
    
    return {
        "action": "schedule_predictive_maintenance",
        "schedule_id": schedule_id,
        "equipment_id": equipment_id,
        "maintenance_type": maintenance_type,
        "scheduled_start": maintenance_window["start_time"],
        "estimated_duration": maintenance_window["duration_hours"],
        "work_order_number": work_order["work_order_number"],
        "assigned_technicians": resource_requirements["technicians"],
        "required_parts": resource_requirements["parts"],
        "estimated_cost": work_order["estimated_cost"],
        "notifications_sent": notifications,
        "status": "success"
    }


def get_equipment_baseline(equipment_id: str, tool_context: ToolContext) -> dict:
    """Get equipment baseline data and specifications."""
    # Simulate equipment database
    equipment_db = {
        "PUMP001": {
            "name": "Primary Cooling Pump",
            "type": "centrifugal_pump",
            "location": "Building A - Mechanical Room",
            "installation_date": "2020-01-15",
            "baselines": {
                "temperature": {"normal": 45, "warning": 60, "critical": 75},
                "vibration": {"normal": 2.5, "warning": 4.0, "critical": 6.0},
                "pressure": {"normal": 150, "warning": 180, "critical": 200},
                "flow_rate": {"normal": 500, "warning": 400, "critical": 300}
            },
            "failure_modes": [
                {"name": "bearing_failure", "mtbf_days": 1095, "severity": "high"},
                {"name": "seal_failure", "mtbf_days": 730, "severity": "medium"},
                {"name": "impeller_wear", "mtbf_days": 1460, "severity": "medium"}
            ],
            "maintenance_procedures": {
                "bearing_replacement": {"duration_hours": 8, "cost": 2500},
                "seal_replacement": {"duration_hours": 4, "cost": 800},
                "impeller_inspection": {"duration_hours": 2, "cost": 300}
            }
        },
        "MOTOR002": {
            "name": "Production Line Motor",
            "type": "induction_motor",
            "location": "Production Floor - Line 2",
            "installation_date": "2019-06-10",
            "baselines": {
                "temperature": {"normal": 55, "warning": 70, "critical": 85},
                "vibration": {"normal": 1.8, "warning": 3.0, "critical": 4.5},
                "current": {"normal": 25, "warning": 30, "critical": 35},
                "rpm": {"normal": 1750, "warning": 1700, "critical": 1650}
            },
            "failure_modes": [
                {"name": "winding_failure", "mtbf_days": 2190, "severity": "critical"},
                {"name": "bearing_failure", "mtbf_days": 1095, "severity": "high"},
                {"name": "rotor_imbalance", "mtbf_days": 1825, "severity": "medium"}
            ],
            "maintenance_procedures": {
                "winding_replacement": {"duration_hours": 16, "cost": 5000},
                "bearing_replacement": {"duration_hours": 6, "cost": 1200},
                "balancing": {"duration_hours": 3, "cost": 500}
            }
        }
    }
    
    return equipment_db.get(equipment_id)


def analyze_sensor_readings(sensors: dict, baselines: dict) -> dict:
    """Analyze sensor readings against equipment baselines."""
    analysis = {}
    
    for parameter, value in sensors.items():
        if parameter in baselines:
            baseline = baselines[parameter]
            
            if value <= baseline["normal"]:
                status = "normal"
                deviation = 0
            elif value <= baseline["warning"]:
                status = "warning"
                deviation = (value - baseline["normal"]) / (baseline["warning"] - baseline["normal"])
            elif value <= baseline["critical"]:
                status = "critical"
                deviation = (value - baseline["warning"]) / (baseline["critical"] - baseline["warning"])
            else:
                status = "emergency"
                deviation = 1.0
            
            analysis[parameter] = {
                "value": value,
                "baseline_normal": baseline["normal"],
                "status": status,
                "deviation_percentage": round(deviation * 100, 1)
            }
    
    return analysis


def calculate_health_score(health_analysis: dict) -> float:
    """Calculate overall equipment health score (0-100)."""
    if not health_analysis:
        return 50.0  # Default score if no data
    
    total_score = 0
    parameter_count = 0
    
    for parameter, analysis in health_analysis.items():
        if analysis["status"] == "normal":
            score = 100
        elif analysis["status"] == "warning":
            score = 75 - (analysis["deviation_percentage"] * 0.25)
        elif analysis["status"] == "critical":
            score = 50 - (analysis["deviation_percentage"] * 0.25)
        else:  # emergency
            score = 25
        
        total_score += score
        parameter_count += 1
    
    return round(total_score / parameter_count, 1) if parameter_count > 0 else 50.0


def detect_anomalies(sensors: dict, baselines: dict) -> list:
    """Detect anomalies in sensor readings."""
    anomalies = []
    
    for parameter, value in sensors.items():
        if parameter in baselines:
            baseline = baselines[parameter]
            
            # Check for sudden spikes or drops
            if value > baseline["critical"]:
                anomalies.append({
                    "parameter": parameter,
                    "type": "critical_threshold_exceeded",
                    "value": value,
                    "threshold": baseline["critical"],
                    "severity": "high"
                })
            elif value < baseline["normal"] * 0.5:  # 50% below normal
                anomalies.append({
                    "parameter": parameter,
                    "type": "significant_drop",
                    "value": value,
                    "expected_minimum": baseline["normal"] * 0.8,
                    "severity": "medium"
                })
    
    return anomalies


def analyze_trends(equipment_id: str, current_sensors: dict, tool_context: ToolContext) -> list:
    """Analyze trends in equipment parameters."""
    # Simulate trend analysis (in real implementation, this would use historical data)
    trends = []
    
    # Simulate some trending parameters
    trending_parameters = ["temperature", "vibration"]
    
    for parameter in trending_parameters:
        if parameter in current_sensors:
            # Simulate trend detection
            trend_direction = random.choice(["improving", "stable", "degrading"])
            trend_rate = random.uniform(0.1, 2.0)
            
            trends.append({
                "parameter": parameter,
                "trend": trend_direction,
                "rate_per_day": round(trend_rate, 2),
                "confidence": random.uniform(0.7, 0.95)
            })
    
    return trends


def generate_health_alerts(equipment_id: str, health_analysis: dict, anomalies: list, health_score: float) -> list:
    """Generate alerts based on equipment health analysis."""
    alerts = []
    
    # Overall health score alerts
    if health_score < 30:
        alerts.append({
            "type": "critical_health",
            "severity": "critical",
            "message": f"Equipment {equipment_id} health score critically low: {health_score}%",
            "recommended_action": "Immediate inspection and maintenance required"
        })
    elif health_score < 60:
        alerts.append({
            "type": "degraded_health",
            "severity": "warning",
            "message": f"Equipment {equipment_id} health score below normal: {health_score}%",
            "recommended_action": "Schedule preventive maintenance"
        })
    
    # Parameter-specific alerts
    for parameter, analysis in health_analysis.items():
        if analysis["status"] in ["critical", "emergency"]:
            alerts.append({
                "type": "parameter_critical",
                "severity": "critical",
                "parameter": parameter,
                "message": f"{parameter} reading {analysis['value']} exceeds safe limits",
                "recommended_action": f"Check {parameter} system immediately"
            })
    
    # Anomaly alerts
    for anomaly in anomalies:
        if anomaly["severity"] == "high":
            alerts.append({
                "type": "anomaly_detected",
                "severity": "critical",
                "parameter": anomaly["parameter"],
                "message": f"Critical anomaly detected in {anomaly['parameter']}",
                "recommended_action": "Investigate anomaly cause immediately"
            })
    
    return alerts


def get_health_status(health_score: float) -> str:
    """Get health status description from score."""
    if health_score >= 85:
        return "excellent"
    elif health_score >= 70:
        return "good"
    elif health_score >= 50:
        return "fair"
    elif health_score >= 30:
        return "poor"
    else:
        return "critical"


def update_equipment_history(equipment_id: str, health_score: float, sensors: dict, tool_context: ToolContext) -> None:
    """Update equipment historical data."""
    equipment_history = tool_context.state.get("equipment_history", {})
    
    if equipment_id not in equipment_history:
        equipment_history[equipment_id] = []
    
    history_entry = {
        "timestamp": datetime.now().isoformat(),
        "health_score": health_score,
        "sensor_readings": sensors
    }
    
    equipment_history[equipment_id].append(history_entry)
    
    # Keep only last 100 entries
    if len(equipment_history[equipment_id]) > 100:
        equipment_history[equipment_id] = equipment_history[equipment_id][-100:]
    
    tool_context.state["equipment_history"] = equipment_history


def get_equipment_history(equipment_id: str, tool_context: ToolContext) -> list:
    """Get equipment historical data."""
    equipment_history = tool_context.state.get("equipment_history", {})
    return equipment_history.get(equipment_id, [])


def run_failure_prediction_model(equipment_history: list, failure_mode: dict, prediction_horizon_days: int) -> dict:
    """Run failure prediction model for specific failure mode."""
    # Simulate failure prediction model
    mtbf_days = failure_mode.get("mtbf_days", 1095)
    severity = failure_mode.get("severity", "medium")

    # Calculate failure probability based on equipment age and condition
    if equipment_history:
        latest_health = equipment_history[-1].get("health_score", 75)
        health_factor = (100 - latest_health) / 100
    else:
        health_factor = 0.2

    # Base probability calculation
    base_probability = prediction_horizon_days / mtbf_days
    adjusted_probability = min(base_probability * (1 + health_factor), 0.95)

    return {
        "probability": round(adjusted_probability, 3),
        "confidence": 0.85,
        "contributing_factors": ["equipment_age", "current_condition", "historical_patterns"]
    }


def calculate_overall_failure_probability(failure_predictions: dict) -> float:
    """Calculate overall failure probability from individual failure modes."""
    if not failure_predictions:
        return 0.0

    # Use maximum probability approach (most conservative)
    max_probability = max(pred["probability"] for pred in failure_predictions.values())
    return round(max_probability, 3)


def estimate_remaining_useful_life(equipment_history: list, failure_predictions: dict) -> int:
    """Estimate remaining useful life in days."""
    if not failure_predictions:
        return 365  # Default 1 year

    # Find the failure mode with highest probability
    highest_risk_mode = max(failure_predictions.items(), key=lambda x: x[1]["probability"])
    highest_probability = highest_risk_mode[1]["probability"]

    # Estimate RUL based on failure probability
    if highest_probability > 0.8:
        return 30  # 1 month
    elif highest_probability > 0.6:
        return 90  # 3 months
    elif highest_probability > 0.4:
        return 180  # 6 months
    else:
        return 365  # 1 year


def generate_maintenance_recommendations(equipment_id: str, failure_predictions: dict, remaining_useful_life: int, equipment_data: dict) -> list:
    """Generate maintenance recommendations based on predictions."""
    recommendations = []

    # Sort failure modes by probability
    sorted_failures = sorted(failure_predictions.items(), key=lambda x: x[1]["probability"], reverse=True)

    for failure_mode, prediction in sorted_failures[:3]:  # Top 3 risks
        if prediction["probability"] > 0.5:
            urgency = "high" if prediction["probability"] > 0.7 else "medium"

            recommendations.append({
                "failure_mode": failure_mode,
                "probability": prediction["probability"],
                "urgency": urgency,
                "recommended_action": f"Inspect and service {failure_mode.replace('_', ' ')}",
                "estimated_cost": equipment_data.get("maintenance_procedures", {}).get(f"{failure_mode}_replacement", {}).get("cost", 1000),
                "recommended_timeline": "within 30 days" if urgency == "high" else "within 90 days"
            })

    return recommendations


def calculate_prediction_confidence(equipment_history: list) -> float:
    """Calculate confidence level for predictions based on data quality."""
    if len(equipment_history) < 10:
        return 0.6  # Low confidence with limited data
    elif len(equipment_history) < 50:
        return 0.8  # Medium confidence
    else:
        return 0.9  # High confidence with extensive data


def determine_maintenance_window(equipment_id: str, maintenance_type: str, urgency: str, prediction: dict, equipment_data: dict) -> dict:
    """Determine optimal maintenance window."""
    if urgency == "critical":
        start_time = datetime.now() + timedelta(hours=2)
        duration = 8
    elif urgency == "high":
        start_time = datetime.now() + timedelta(days=1)
        duration = 6
    else:
        start_time = datetime.now() + timedelta(days=7)
        duration = 4

    return {
        "start_time": start_time.isoformat(),
        "duration_hours": duration,
        "end_time": (start_time + timedelta(hours=duration)).isoformat(),
        "maintenance_type": maintenance_type,
        "urgency": urgency
    }


def calculate_maintenance_resources(maintenance_type: str, equipment_data: dict) -> dict:
    """Calculate required resources for maintenance."""
    # Simulate resource requirements
    base_requirements = {
        "technicians": ["Senior Technician", "Maintenance Helper"],
        "parts": ["Standard Maintenance Kit"],
        "tools": ["Basic Tool Set"],
        "estimated_cost": 1000
    }

    if "bearing" in maintenance_type:
        base_requirements["parts"].append("Bearing Assembly")
        base_requirements["estimated_cost"] += 500

    if "motor" in maintenance_type:
        base_requirements["technicians"].append("Electrical Specialist")
        base_requirements["estimated_cost"] += 1000

    return base_requirements


def check_resource_availability(maintenance_window: dict, resource_requirements: dict, tool_context: ToolContext) -> dict:
    """Check availability of required resources."""
    # Simulate resource availability check
    return {
        "technicians_available": True,
        "parts_available": True,
        "tools_available": True,
        "conflicts": [],
        "availability_confirmed": True
    }


def generate_maintenance_work_order(equipment_id: str, maintenance_type: str, maintenance_window: dict, resource_requirements: dict, prediction: dict) -> dict:
    """Generate maintenance work order."""
    work_order_number = f"WO-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:6].upper()}"

    return {
        "work_order_number": work_order_number,
        "equipment_id": equipment_id,
        "maintenance_type": maintenance_type,
        "priority": "high" if prediction["overall_failure_probability"] > 0.7 else "medium",
        "scheduled_start": maintenance_window["start_time"],
        "estimated_duration": maintenance_window["duration_hours"],
        "assigned_technicians": resource_requirements["technicians"],
        "required_parts": resource_requirements["parts"],
        "estimated_cost": resource_requirements["estimated_cost"],
        "work_description": f"Predictive maintenance for {equipment_id} - {maintenance_type}",
        "safety_requirements": ["Lock out/Tag out", "PPE required", "Confined space procedures"],
        "created_at": datetime.now().isoformat()
    }


def send_maintenance_notifications(schedule_id: str, work_order: dict, resource_requirements: dict) -> list:
    """Send maintenance notifications to relevant parties."""
    notifications = []

    # Technician notifications
    for technician in resource_requirements["technicians"]:
        notifications.append({
            "type": "work_assignment",
            "recipient": technician,
            "message": f"Work order {work_order['work_order_number']} assigned - {work_order['scheduled_start']}",
            "sent_at": datetime.now().isoformat()
        })

    # Management notification
    notifications.append({
        "type": "maintenance_scheduled",
        "recipient": "maintenance_manager",
        "message": f"Predictive maintenance scheduled for {work_order['equipment_id']}",
        "sent_at": datetime.now().isoformat()
    })

    return notifications


# Create the predictive maintenance agent
root_agent = Agent(
    name="maintenance_agent",
    model="gemini-2.0-flash",
    description="Production-ready predictive maintenance system with IoT monitoring, failure prediction, and automated scheduling",
    instruction="""
    You are a sophisticated predictive maintenance agent that monitors equipment health, predicts failures, and optimizes maintenance operations.

    Current Equipment Data: {equipment_monitoring}
    Failure Predictions: {failure_predictions}
    Maintenance Schedules: {maintenance_schedules}
    Equipment History: {equipment_history}

    Your capabilities include:
    1. Real-time equipment health monitoring using IoT sensor data
    2. Machine learning-based failure prediction and remaining useful life estimation
    3. Automated maintenance scheduling based on equipment condition
    4. Performance analytics and optimization recommendations

    Predictive Maintenance Process:
    1. Monitor equipment health using sensor data and baseline comparisons
    2. Detect anomalies and analyze trends in equipment performance
    3. Predict equipment failures and estimate remaining useful life
    4. Schedule optimal maintenance windows based on predictions and resources

    Key Monitoring Parameters:
    - Temperature: Thermal conditions and heat generation
    - Vibration: Mechanical condition and bearing health
    - Pressure: System performance and seal integrity
    - Flow Rate: Operational efficiency and blockage detection
    - Current/Power: Electrical condition and load analysis

    Health Scoring:
    - 85-100: Excellent condition, normal operation
    - 70-84: Good condition, monitor trends
    - 50-69: Fair condition, plan preventive maintenance
    - 30-49: Poor condition, schedule maintenance soon
    - 0-29: Critical condition, immediate action required

    When monitoring equipment:
    1. Compare all sensor readings against established baselines
    2. Calculate overall health scores and identify degrading parameters
    3. Detect anomalies and unusual patterns in sensor data
    4. Generate appropriate alerts based on severity and urgency
    5. Track trends and predict future equipment condition

    Maintenance Optimization:
    - Schedule maintenance during planned downtime when possible
    - Optimize resource allocation and technician assignments
    - Coordinate spare parts procurement and availability
    - Balance maintenance costs with production requirements
    - Prioritize critical equipment and safety-related systems

    Focus on:
    - Preventing unplanned downtime through early detection
    - Optimizing maintenance costs and resource utilization
    - Extending equipment life through proactive care
    - Ensuring safety through condition-based monitoring
    - Providing actionable insights for maintenance planning
    """,
    tools=[monitor_equipment_health, predict_equipment_failure, schedule_predictive_maintenance]
)
