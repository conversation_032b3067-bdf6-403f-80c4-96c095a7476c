from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext


def configure_custom_model(model_name: str, model_endpoint: str, parameters: str, tool_context: ToolContext) -> dict:
    """Configure a custom model for use with the agent."""
    print(f"--- Tool: configure_custom_model called for {model_name} ---")
    
    custom_models = tool_context.state.get("custom_models", {})
    custom_models[model_name] = {
        "endpoint": model_endpoint,
        "parameters": parameters,
        "configured_at": "2024-01-01T00:00:00"
    }
    tool_context.state["custom_models"] = custom_models
    
    return {
        "action": "configure_custom_model",
        "model_name": model_name,
        "message": f"Custom model {model_name} configured successfully",
        "status": "success"
    }


def test_custom_model(model_name: str, test_prompt: str, tool_context: ToolContext) -> dict:
    """Test a configured custom model with a sample prompt."""
    print(f"--- Tool: test_custom_model called for {model_name} ---")
    
    custom_models = tool_context.state.get("custom_models", {})
    if model_name not in custom_models:
        return {
            "action": "test_custom_model",
            "error": f"Custom model {model_name} not found",
            "status": "error"
        }
    
    # Simulate model response
    mock_response = f"Custom model {model_name} response to: {test_prompt}"
    
    return {
        "action": "test_custom_model",
        "model_name": model_name,
        "test_prompt": test_prompt,
        "model_response": mock_response,
        "status": "success"
    }


# Create the custom model agent
root_agent = Agent(
    name="custom_model_agent",
    model="gemini-2.0-flash",
    description="Agent that demonstrates integration with custom or fine-tuned models",
    instruction="""
    You are a custom model agent that helps users integrate and work with custom or fine-tuned models.
    
    Your capabilities include:
    1. Configuring custom model endpoints
    2. Testing custom models with sample prompts
    3. Managing model parameters and configurations
    4. Monitoring model performance
    
    Use the available tools to help users set up and test their custom models.
    """,
    tools=[configure_custom_model, test_custom_model]
)
