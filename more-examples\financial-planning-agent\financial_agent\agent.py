from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext
from datetime import datetime, timedelta
import uuid
import json
import math


def analyze_financial_health(client_data: str, tool_context: ToolContext) -> dict:
    """Analyze client's overall financial health and provide comprehensive assessment."""
    print(f"--- Tool: analyze_financial_health called ---")

    try:
        client_info = json.loads(client_data)
    except json.JSONDecodeError:
        return {
            "action": "analyze_financial_health",
            "error": "Invalid client data format",
            "status": "error"
        }

    # Calculate key financial metrics
    financial_metrics = calculate_financial_metrics(client_info)

    # Assess financial health across different areas
    health_assessment = {
        "net_worth": assess_net_worth(financial_metrics),
        "cash_flow": assess_cash_flow(financial_metrics),
        "debt_management": assess_debt_situation(financial_metrics),
        "emergency_fund": assess_emergency_fund(financial_metrics),
        "retirement_readiness": assess_retirement_readiness(client_info, financial_metrics),
        "insurance_coverage": assess_insurance_adequacy(client_info)
    }

    # Calculate overall financial health score
    overall_score = calculate_overall_health_score(health_assessment)

    # Generate recommendations
    recommendations = generate_financial_recommendations(health_assessment, client_info)

    # Store analysis
    analysis_id = f"FHA-{str(uuid.uuid4())[:8].upper()}"
    financial_analyses = tool_context.state.get("financial_analyses", {})
    financial_analyses[analysis_id] = {
        "analysis_id": analysis_id,
        "client_id": client_info.get("client_id", "unknown"),
        "analyzed_at": datetime.now().isoformat(),
        "financial_metrics": financial_metrics,
        "health_assessment": health_assessment,
        "overall_score": overall_score,
        "recommendations": recommendations,
        "next_review_date": (datetime.now() + timedelta(days=90)).isoformat()
    }
    tool_context.state["financial_analyses"] = financial_analyses

    return {
        "action": "analyze_financial_health",
        "analysis_id": analysis_id,
        "client_id": client_info.get("client_id"),
        "overall_health_score": overall_score,
        "health_assessment": health_assessment,
        "key_recommendations": recommendations[:3],  # Top 3 recommendations
        "financial_metrics": financial_metrics,
        "next_review_date": financial_analyses[analysis_id]["next_review_date"],
        "status": "success"
    }


def create_investment_plan(client_id: str, investment_goals: str, risk_tolerance: str, tool_context: ToolContext) -> dict:
    """Create personalized investment plan based on goals and risk tolerance."""
    print(f"--- Tool: create_investment_plan called for client {client_id} ---")

    try:
        goals = json.loads(investment_goals)
    except json.JSONDecodeError:
        return {
            "action": "create_investment_plan",
            "error": "Invalid investment goals format",
            "status": "error"
        }

    # Get client financial data
    financial_analyses = tool_context.state.get("financial_analyses", {})
    client_analysis = None
    for analysis in financial_analyses.values():
        if analysis["client_id"] == client_id:
            client_analysis = analysis
            break

    if not client_analysis:
        return {
            "action": "create_investment_plan",
            "error": f"No financial analysis found for client {client_id}",
            "status": "error"
        }

    # Assess risk tolerance
    risk_profile = assess_risk_tolerance(risk_tolerance, goals)

    # Create asset allocation strategy
    asset_allocation = create_asset_allocation(risk_profile, goals)

    # Recommend specific investments
    investment_recommendations = recommend_investments(asset_allocation, goals)

    # Calculate projected returns and scenarios
    projections = calculate_investment_projections(asset_allocation, goals)

    # Create implementation timeline
    implementation_plan = create_implementation_timeline(investment_recommendations, goals)

    # Store investment plan
    plan_id = f"INV-{str(uuid.uuid4())[:8].upper()}"
    investment_plans = tool_context.state.get("investment_plans", {})
    investment_plans[plan_id] = {
        "plan_id": plan_id,
        "client_id": client_id,
        "created_at": datetime.now().isoformat(),
        "investment_goals": goals,
        "risk_profile": risk_profile,
        "asset_allocation": asset_allocation,
        "investment_recommendations": investment_recommendations,
        "projections": projections,
        "implementation_plan": implementation_plan,
        "next_review_date": (datetime.now() + timedelta(days=180)).isoformat()
    }
    tool_context.state["investment_plans"] = investment_plans

    return {
        "action": "create_investment_plan",
        "plan_id": plan_id,
        "client_id": client_id,
        "risk_profile": risk_profile,
        "recommended_allocation": asset_allocation,
        "key_investments": investment_recommendations[:5],  # Top 5 recommendations
        "projected_annual_return": projections["expected_annual_return"],
        "implementation_timeline": implementation_plan,
        "next_review_date": investment_plans[plan_id]["next_review_date"],
        "status": "success"
    }


def plan_retirement_strategy(client_id: str, retirement_goals: str, current_age: int, tool_context: ToolContext) -> dict:
    """Create comprehensive retirement planning strategy."""
    print(f"--- Tool: plan_retirement_strategy called for client {client_id} ---")

    try:
        goals = json.loads(retirement_goals)
    except json.JSONDecodeError:
        return {
            "action": "plan_retirement_strategy",
            "error": "Invalid retirement goals format",
            "status": "error"
        }

    # Get client financial data
    financial_analyses = tool_context.state.get("financial_analyses", {})
    client_analysis = None
    for analysis in financial_analyses.values():
        if analysis["client_id"] == client_id:
            client_analysis = analysis
            break

    if not client_analysis:
        return {
            "action": "plan_retirement_strategy",
            "error": f"No financial analysis found for client {client_id}",
            "status": "error"
        }

    retirement_age = goals.get("target_retirement_age", 65)
    years_to_retirement = retirement_age - current_age

    # Calculate retirement needs
    retirement_needs = calculate_retirement_needs(goals, current_age)

    # Assess current retirement savings
    current_savings_assessment = assess_current_retirement_savings(client_analysis["financial_metrics"])

    # Calculate retirement gap
    retirement_gap = calculate_retirement_gap(retirement_needs, current_savings_assessment, years_to_retirement)

    # Create savings strategy
    savings_strategy = create_retirement_savings_strategy(retirement_gap, years_to_retirement, client_analysis)

    # Social Security optimization
    social_security_strategy = optimize_social_security_strategy(goals, current_age)

    # Healthcare planning
    healthcare_planning = plan_retirement_healthcare(goals, retirement_age)

    # Store retirement plan
    plan_id = f"RET-{str(uuid.uuid4())[:8].upper()}"
    retirement_plans = tool_context.state.get("retirement_plans", {})
    retirement_plans[plan_id] = {
        "plan_id": plan_id,
        "client_id": client_id,
        "created_at": datetime.now().isoformat(),
        "retirement_goals": goals,
        "current_age": current_age,
        "years_to_retirement": years_to_retirement,
        "retirement_needs": retirement_needs,
        "current_savings": current_savings_assessment,
        "retirement_gap": retirement_gap,
        "savings_strategy": savings_strategy,
        "social_security_strategy": social_security_strategy,
        "healthcare_planning": healthcare_planning,
        "next_review_date": (datetime.now() + timedelta(days=365)).isoformat()
    }
    tool_context.state["retirement_plans"] = retirement_plans

    return {
        "action": "plan_retirement_strategy",
        "plan_id": plan_id,
        "client_id": client_id,
        "years_to_retirement": years_to_retirement,
        "retirement_needs": retirement_needs,
        "current_savings_value": current_savings_assessment["total_value"],
        "retirement_gap": retirement_gap,
        "monthly_savings_needed": savings_strategy["monthly_contribution_needed"],
        "social_security_optimization": social_security_strategy,
        "key_recommendations": savings_strategy["recommendations"][:3],
        "next_review_date": retirement_plans[plan_id]["next_review_date"],
        "status": "success"
    }


def calculate_financial_metrics(client_info: dict) -> dict:
    """Calculate key financial metrics from client data."""
    # Assets
    assets = client_info.get("assets", {})
    total_assets = sum(assets.values())

    # Liabilities
    liabilities = client_info.get("liabilities", {})
    total_liabilities = sum(liabilities.values())

    # Income and expenses
    annual_income = client_info.get("annual_income", 0)
    monthly_expenses = client_info.get("monthly_expenses", 0)
    annual_expenses = monthly_expenses * 12

    # Calculate key ratios
    net_worth = total_assets - total_liabilities
    debt_to_income = (total_liabilities / annual_income) if annual_income > 0 else 0
    savings_rate = ((annual_income - annual_expenses) / annual_income) if annual_income > 0 else 0
    emergency_fund_months = assets.get("emergency_fund", 0) / monthly_expenses if monthly_expenses > 0 else 0

    return {
        "total_assets": total_assets,
        "total_liabilities": total_liabilities,
        "net_worth": net_worth,
        "annual_income": annual_income,
        "annual_expenses": annual_expenses,
        "monthly_expenses": monthly_expenses,
        "debt_to_income_ratio": debt_to_income,
        "savings_rate": savings_rate,
        "emergency_fund_months": emergency_fund_months
    }


def assess_net_worth(metrics: dict) -> dict:
    """Assess net worth relative to age and income."""
    net_worth = metrics["net_worth"]
    annual_income = metrics["annual_income"]

    # Rule of thumb: net worth should be annual income * (age / 10)
    # For simplicity, assume age 35 as baseline
    target_net_worth = annual_income * 3.5

    if net_worth >= target_net_worth:
        score = 100
        status = "excellent"
        message = "Net worth is on track or ahead of typical benchmarks"
    elif net_worth >= target_net_worth * 0.75:
        score = 75
        status = "good"
        message = "Net worth is slightly below target but manageable"
    elif net_worth >= target_net_worth * 0.5:
        score = 50
        status = "needs_improvement"
        message = "Net worth is below target, focus on asset building"
    else:
        score = 25
        status = "poor"
        message = "Net worth significantly below target, immediate action needed"

    return {
        "score": score,
        "status": status,
        "message": message,
        "current_net_worth": net_worth,
        "target_net_worth": target_net_worth
    }


def assess_cash_flow(metrics: dict) -> dict:
    """Assess cash flow and savings rate."""
    savings_rate = metrics["savings_rate"]

    if savings_rate >= 0.20:  # 20% or more
        score = 100
        status = "excellent"
        message = "Excellent savings rate, well positioned for financial goals"
    elif savings_rate >= 0.15:  # 15-19%
        score = 80
        status = "good"
        message = "Good savings rate, on track for most financial goals"
    elif savings_rate >= 0.10:  # 10-14%
        score = 60
        status = "fair"
        message = "Adequate savings rate, consider increasing if possible"
    elif savings_rate >= 0.05:  # 5-9%
        score = 40
        status = "needs_improvement"
        message = "Low savings rate, budget review recommended"
    else:
        score = 20
        status = "poor"
        message = "Negative or very low savings, immediate budget action needed"

    return {
        "score": score,
        "status": status,
        "message": message,
        "current_savings_rate": round(savings_rate * 100, 1),
        "target_savings_rate": 20.0
    }


def assess_debt_situation(metrics: dict) -> dict:
    """Assess debt-to-income ratio and debt management."""
    debt_to_income = metrics["debt_to_income_ratio"]

    if debt_to_income <= 0.20:  # 20% or less
        score = 100
        status = "excellent"
        message = "Low debt burden, excellent debt management"
    elif debt_to_income <= 0.30:  # 21-30%
        score = 80
        status = "good"
        message = "Manageable debt levels"
    elif debt_to_income <= 0.40:  # 31-40%
        score = 60
        status = "fair"
        message = "Moderate debt burden, monitor carefully"
    elif debt_to_income <= 0.50:  # 41-50%
        score = 40
        status = "needs_improvement"
        message = "High debt burden, debt reduction plan recommended"
    else:
        score = 20
        status = "poor"
        message = "Very high debt burden, immediate debt reduction required"

    return {
        "score": score,
        "status": status,
        "message": message,
        "current_debt_to_income": round(debt_to_income * 100, 1),
        "target_debt_to_income": 20.0
    }


def assess_emergency_fund(metrics: dict) -> dict:
    """Assess emergency fund adequacy."""
    emergency_months = metrics["emergency_fund_months"]

    if emergency_months >= 6:
        score = 100
        status = "excellent"
        message = "Excellent emergency fund coverage"
    elif emergency_months >= 3:
        score = 75
        status = "good"
        message = "Adequate emergency fund, consider building to 6 months"
    elif emergency_months >= 1:
        score = 50
        status = "needs_improvement"
        message = "Minimal emergency fund, should be increased"
    else:
        score = 25
        status = "poor"
        message = "No emergency fund, immediate priority to build one"

    return {
        "score": score,
        "status": status,
        "message": message,
        "current_months_covered": round(emergency_months, 1),
        "target_months": 6.0
    }


def assess_retirement_readiness(client_info: dict, metrics: dict) -> dict:
    """Assess retirement savings progress."""
    age = client_info.get("age", 35)
    annual_income = metrics["annual_income"]
    retirement_assets = client_info.get("assets", {}).get("retirement_accounts", 0)

    # Rule of thumb: retirement savings should be annual income * (age / 10)
    target_retirement_savings = annual_income * (age / 10)

    if retirement_assets >= target_retirement_savings:
        score = 100
        status = "excellent"
        message = "Retirement savings on track or ahead"
    elif retirement_assets >= target_retirement_savings * 0.75:
        score = 75
        status = "good"
        message = "Retirement savings slightly behind but manageable"
    elif retirement_assets >= target_retirement_savings * 0.5:
        score = 50
        status = "needs_improvement"
        message = "Retirement savings behind target, increase contributions"
    else:
        score = 25
        status = "poor"
        message = "Retirement savings significantly behind, immediate action needed"

    return {
        "score": score,
        "status": status,
        "message": message,
        "current_retirement_savings": retirement_assets,
        "target_retirement_savings": target_retirement_savings
    }


def assess_insurance_adequacy(client_info: dict) -> dict:
    """Assess insurance coverage adequacy."""
    insurance = client_info.get("insurance", {})
    annual_income = client_info.get("annual_income", 0)

    # Life insurance should be 10x annual income
    life_insurance = insurance.get("life_insurance", 0)
    target_life_insurance = annual_income * 10

    # Disability insurance should cover 60-70% of income
    disability_insurance = insurance.get("disability_insurance", 0)
    target_disability = annual_income * 0.65

    life_coverage_ratio = life_insurance / target_life_insurance if target_life_insurance > 0 else 0
    disability_coverage_ratio = disability_insurance / target_disability if target_disability > 0 else 0

    overall_coverage = (life_coverage_ratio + disability_coverage_ratio) / 2

    if overall_coverage >= 0.8:
        score = 100
        status = "excellent"
        message = "Insurance coverage is adequate"
    elif overall_coverage >= 0.6:
        score = 75
        status = "good"
        message = "Insurance coverage is mostly adequate"
    elif overall_coverage >= 0.4:
        score = 50
        status = "needs_improvement"
        message = "Insurance coverage has gaps"
    else:
        score = 25
        status = "poor"
        message = "Insurance coverage is inadequate"

    return {
        "score": score,
        "status": status,
        "message": message,
        "life_insurance_gap": max(0, target_life_insurance - life_insurance),
        "disability_insurance_gap": max(0, target_disability - disability_insurance)
    }


def calculate_overall_health_score(health_assessment: dict) -> dict:
    """Calculate overall financial health score."""
    weights = {
        "net_worth": 0.20,
        "cash_flow": 0.25,
        "debt_management": 0.20,
        "emergency_fund": 0.15,
        "retirement_readiness": 0.15,
        "insurance_coverage": 0.05
    }

    total_score = 0
    for category, weight in weights.items():
        if category in health_assessment:
            total_score += health_assessment[category]["score"] * weight

    if total_score >= 85:
        grade = "A"
        status = "excellent"
        message = "Excellent financial health across all areas"
    elif total_score >= 70:
        grade = "B"
        status = "good"
        message = "Good financial health with minor areas for improvement"
    elif total_score >= 55:
        grade = "C"
        status = "fair"
        message = "Fair financial health, several areas need attention"
    elif total_score >= 40:
        grade = "D"
        status = "needs_improvement"
        message = "Financial health needs significant improvement"
    else:
        grade = "F"
        status = "poor"
        message = "Poor financial health, immediate action required"

    return {
        "score": round(total_score, 1),
        "grade": grade,
        "status": status,
        "message": message
    }


def generate_financial_recommendations(health_assessment: dict, client_info: dict) -> list:
    """Generate prioritized financial recommendations."""
    recommendations = []

    # Emergency fund recommendations
    if health_assessment["emergency_fund"]["score"] < 75:
        recommendations.append({
            "priority": "high",
            "category": "emergency_fund",
            "recommendation": "Build emergency fund to 6 months of expenses",
            "action_steps": [
                "Open high-yield savings account",
                "Set up automatic transfer of $500/month",
                "Reduce discretionary spending temporarily"
            ]
        })

    # Debt management recommendations
    if health_assessment["debt_management"]["score"] < 70:
        recommendations.append({
            "priority": "high",
            "category": "debt_reduction",
            "recommendation": "Implement debt reduction strategy",
            "action_steps": [
                "List all debts with interest rates",
                "Use debt avalanche method (highest interest first)",
                "Consider debt consolidation if beneficial"
            ]
        })

    # Retirement savings recommendations
    if health_assessment["retirement_readiness"]["score"] < 75:
        recommendations.append({
            "priority": "medium",
            "category": "retirement",
            "recommendation": "Increase retirement contributions",
            "action_steps": [
                "Maximize employer 401(k) match",
                "Increase contribution by 1% annually",
                "Consider Roth IRA for tax diversification"
            ]
        })

    # Investment recommendations
    if health_assessment["cash_flow"]["score"] >= 70:
        recommendations.append({
            "priority": "medium",
            "category": "investment",
            "recommendation": "Develop investment strategy",
            "action_steps": [
                "Open taxable investment account",
                "Invest in diversified index funds",
                "Rebalance portfolio quarterly"
            ]
        })

    # Insurance recommendations
    if health_assessment["insurance_coverage"]["score"] < 70:
        recommendations.append({
            "priority": "medium",
            "category": "insurance",
            "recommendation": "Review and update insurance coverage",
            "action_steps": [
                "Get life insurance quotes",
                "Review disability insurance options",
                "Consider umbrella liability policy"
            ]
        })

    return recommendations


def assess_risk_tolerance(risk_tolerance: str, goals: dict) -> dict:
    """Assess investor risk tolerance and create risk profile."""
    risk_level = risk_tolerance.lower()

    if risk_level == "conservative":
        risk_score = 3
        description = "Conservative investor seeking capital preservation"
        max_equity_allocation = 40
    elif risk_level == "moderate":
        risk_score = 5
        description = "Moderate investor balancing growth and stability"
        max_equity_allocation = 70
    elif risk_level == "aggressive":
        risk_score = 8
        description = "Aggressive investor seeking maximum growth"
        max_equity_allocation = 90
    else:
        risk_score = 5
        description = "Moderate investor (default)"
        max_equity_allocation = 70

    # Adjust based on time horizon
    time_horizon = goals.get("time_horizon_years", 10)
    if time_horizon > 20:
        max_equity_allocation = min(max_equity_allocation + 10, 90)
    elif time_horizon < 5:
        max_equity_allocation = max(max_equity_allocation - 20, 20)

    return {
        "risk_level": risk_level,
        "risk_score": risk_score,
        "description": description,
        "max_equity_allocation": max_equity_allocation,
        "time_horizon": time_horizon
    }


def create_asset_allocation(risk_profile: dict, goals: dict) -> dict:
    """Create optimal asset allocation based on risk profile."""
    max_equity = risk_profile["max_equity_allocation"]

    # Base allocation
    if max_equity >= 80:
        allocation = {
            "us_stocks": 50,
            "international_stocks": 20,
            "bonds": 20,
            "alternatives": 10
        }
    elif max_equity >= 60:
        allocation = {
            "us_stocks": 40,
            "international_stocks": 15,
            "bonds": 35,
            "alternatives": 10
        }
    else:
        allocation = {
            "us_stocks": 25,
            "international_stocks": 10,
            "bonds": 55,
            "alternatives": 10
        }

    return allocation


def recommend_investments(asset_allocation: dict, goals: dict) -> list:
    """Recommend specific investments based on allocation."""
    recommendations = []

    # US Stocks
    if asset_allocation["us_stocks"] > 0:
        recommendations.append({
            "category": "us_stocks",
            "allocation_percentage": asset_allocation["us_stocks"],
            "investment": "Vanguard Total Stock Market Index (VTI)",
            "expense_ratio": 0.03,
            "description": "Broad US stock market exposure"
        })

    # International Stocks
    if asset_allocation["international_stocks"] > 0:
        recommendations.append({
            "category": "international_stocks",
            "allocation_percentage": asset_allocation["international_stocks"],
            "investment": "Vanguard Total International Stock Index (VTIAX)",
            "expense_ratio": 0.11,
            "description": "International developed and emerging markets"
        })

    # Bonds
    if asset_allocation["bonds"] > 0:
        recommendations.append({
            "category": "bonds",
            "allocation_percentage": asset_allocation["bonds"],
            "investment": "Vanguard Total Bond Market Index (VBTLX)",
            "expense_ratio": 0.05,
            "description": "Broad US bond market exposure"
        })

    # Alternatives
    if asset_allocation["alternatives"] > 0:
        recommendations.append({
            "category": "alternatives",
            "allocation_percentage": asset_allocation["alternatives"],
            "investment": "Vanguard Real Estate Index (VGSLX)",
            "expense_ratio": 0.12,
            "description": "Real estate investment trusts (REITs)"
        })

    return recommendations


def calculate_investment_projections(asset_allocation: dict, goals: dict) -> dict:
    """Calculate investment projections and scenarios."""
    # Expected returns by asset class
    expected_returns = {
        "us_stocks": 0.10,
        "international_stocks": 0.09,
        "bonds": 0.04,
        "alternatives": 0.08
    }

    # Calculate weighted expected return
    portfolio_return = 0
    for asset, allocation in asset_allocation.items():
        portfolio_return += (allocation / 100) * expected_returns.get(asset, 0.06)

    # Calculate scenarios
    conservative_return = portfolio_return - 0.02
    optimistic_return = portfolio_return + 0.02

    time_horizon = goals.get("time_horizon_years", 10)
    initial_investment = goals.get("initial_investment", 10000)
    monthly_contribution = goals.get("monthly_contribution", 500)

    # Calculate future values
    scenarios = {}
    for scenario, annual_return in [
        ("conservative", conservative_return),
        ("expected", portfolio_return),
        ("optimistic", optimistic_return)
    ]:
        future_value = calculate_future_value(initial_investment, monthly_contribution, annual_return, time_horizon)
        scenarios[scenario] = {
            "annual_return": round(annual_return * 100, 1),
            "future_value": round(future_value, 0),
            "total_contributions": initial_investment + (monthly_contribution * 12 * time_horizon),
            "investment_gains": round(future_value - initial_investment - (monthly_contribution * 12 * time_horizon), 0)
        }

    return {
        "expected_annual_return": round(portfolio_return * 100, 1),
        "scenarios": scenarios,
        "time_horizon_years": time_horizon
    }


def calculate_future_value(initial: float, monthly: float, annual_rate: float, years: int) -> float:
    """Calculate future value of investment with regular contributions."""
    monthly_rate = annual_rate / 12
    months = years * 12

    # Future value of initial investment
    fv_initial = initial * ((1 + annual_rate) ** years)

    # Future value of monthly contributions (annuity)
    if monthly_rate > 0:
        fv_monthly = monthly * (((1 + monthly_rate) ** months - 1) / monthly_rate)
    else:
        fv_monthly = monthly * months

    return fv_initial + fv_monthly


def create_implementation_timeline(recommendations: list, goals: dict) -> list:
    """Create implementation timeline for investment plan."""
    timeline = []

    timeline.append({
        "phase": "immediate",
        "timeframe": "Week 1-2",
        "actions": [
            "Open investment accounts (401k, IRA, taxable)",
            "Set up automatic contributions",
            "Implement core portfolio allocation"
        ]
    })

    timeline.append({
        "phase": "short_term",
        "timeframe": "Month 1-3",
        "actions": [
            "Monitor initial performance",
            "Adjust contribution amounts if needed",
            "Review and optimize tax strategies"
        ]
    })

    timeline.append({
        "phase": "ongoing",
        "timeframe": "Quarterly",
        "actions": [
            "Rebalance portfolio to target allocation",
            "Review performance vs benchmarks",
            "Adjust strategy based on life changes"
        ]
    })

    return timeline


# Create the financial planning agent
root_agent = Agent(
    name="financial_agent",
    model="gemini-2.0-flash",
    description="Production-ready personal financial advisory system with comprehensive planning, investment advice, and regulatory compliance",
    instruction="""
    You are a sophisticated financial planning agent that provides comprehensive financial advice while maintaining fiduciary standards and regulatory compliance.

    Current Client Data: {financial_analyses}
    Investment Plans: {investment_plans}
    Retirement Plans: {retirement_plans}

    Your capabilities include:
    1. Comprehensive financial health analysis and assessment
    2. Personalized investment planning with risk-appropriate allocations
    3. Retirement planning with gap analysis and savings strategies
    4. Goal-based financial planning and progress tracking

    Financial Planning Process:
    1. Analyze overall financial health across all key areas
    2. Create risk-appropriate investment strategies
    3. Develop comprehensive retirement planning
    4. Provide ongoing monitoring and adjustments

    Key Financial Areas:
    - Net Worth: Asset building and debt management
    - Cash Flow: Income optimization and expense management
    - Emergency Fund: Liquidity planning and risk management
    - Retirement: Long-term savings and income planning
    - Insurance: Risk protection and coverage adequacy
    - Investments: Growth strategies and portfolio optimization

    Investment Philosophy:
    - Diversified, low-cost index fund approach
    - Risk-appropriate asset allocation based on goals and timeline
    - Tax-efficient investment strategies
    - Regular rebalancing and monitoring

    When providing financial advice:
    1. Always assess complete financial picture before recommendations
    2. Prioritize emergency fund and debt reduction before investments
    3. Match investment risk to client's tolerance and timeline
    4. Consider tax implications of all strategies
    5. Provide clear implementation steps and timelines

    Maintain fiduciary standards:
    - Always act in client's best interest
    - Provide transparent fee and cost information
    - Disclose any potential conflicts of interest
    - Base recommendations on client's specific situation
    - Document all advice and rationale

    Focus on:
    - Evidence-based financial planning strategies
    - Long-term wealth building and preservation
    - Risk management and protection planning
    - Tax-efficient implementation
    - Regular monitoring and plan adjustments
    """,
    tools=[analyze_financial_health, create_investment_plan, plan_retirement_strategy]
)