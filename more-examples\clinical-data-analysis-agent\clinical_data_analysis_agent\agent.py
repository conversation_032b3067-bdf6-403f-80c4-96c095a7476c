from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext
from typing import Dict, Any, List, Optional
import json
from datetime import datetime

# Import local modules
from .clinical_analyzer import analyze_clinical_data
from .diagnosis_engine import generate_diagnosis
from .drug_interaction_checker import check_drug_interactions
from .risk_assessor import assess_patient_risk


def analyze_medical_record(patient_id: str, record_data: str, tool_context: ToolContext) -> dict:
    """Analyze a patient's medical record and extract key clinical insights."""
    print(f"--- Tool: analyze_medical_record called for patient {patient_id} ---")
    
    try:
        medical_data = json.loads(record_data) if record_data else {}
    except json.JSONDecodeError:
        return {
            "action": "analyze_medical_record",
            "error": "Invalid medical record format",
            "status": "error"
        }
    
    # Extract key clinical information
    demographics = medical_data.get("demographics", {})
    vitals = medical_data.get("vitals", {})
    lab_results = medical_data.get("lab_results", [])
    medications = medical_data.get("medications", [])
    conditions = medical_data.get("conditions", [])
    
    # Perform clinical analysis
    analysis_results = analyze_clinical_data(demographics, vitals, lab_results, conditions)
    
    return {
        "action": "analyze_medical_record",
        "patient_id": patient_id,
        "analysis_timestamp": datetime.now().isoformat(),
        "clinical_insights": analysis_results,
        "abnormal_findings": identify_abnormal_findings(vitals, lab_results),
        "status": "success"
    }


def suggest_diagnosis(symptoms: str, patient_data: str, tool_context: ToolContext) -> dict:
    """Generate potential diagnoses based on symptoms and patient data."""
    print(f"--- Tool: suggest_diagnosis called ---")
    
    symptom_list = [s.strip() for s in symptoms.split(',')]
    
    try:
        patient_info = json.loads(patient_data) if patient_data else {}
    except json.JSONDecodeError:
        patient_info = {}
    
    # Generate differential diagnoses
    diagnoses = generate_diagnosis(symptom_list, patient_info)
    
    return {
        "action": "suggest_diagnosis",
        "symptoms": symptom_list,
        "differential_diagnoses": diagnoses,
        "confidence_scores": calculate_confidence_scores(diagnoses, symptom_list, patient_info),
        "recommended_tests": recommend_diagnostic_tests(diagnoses),
        "status": "success"
    }


def check_medication_interactions(medication_list: str, patient_data: str, tool_context: ToolContext) -> dict:
    """Check for potential drug interactions in a medication list."""
    print(f"--- Tool: check_medication_interactions called ---")
    
    medications = [m.strip() for m in medication_list.split(',')]
    
    try:
        patient_info = json.loads(patient_data) if patient_data else {}
    except json.JSONDecodeError:
        patient_info = {}
    
    # Check for drug interactions
    interactions = check_drug_interactions(medications, patient_info)
    
    return {
        "action": "check_medication_interactions",
        "medications": medications,
        "interactions": interactions,
        "severity_levels": categorize_interaction_severity(interactions),
        "recommendations": generate_medication_recommendations(interactions),
        "status": "success"
    }


def evaluate_patient_risk(patient_id: str, condition: str, patient_data: str, tool_context: ToolContext) -> dict:
    """Evaluate patient risk for specific conditions."""
    print(f"--- Tool: evaluate_patient_risk called for patient {patient_id} ---")
    
    try:
        patient_info = json.loads(patient_data) if patient_data else {}
    except json.JSONDecodeError:
        return {
            "action": "evaluate_patient_risk",
            "error": "Invalid patient data format",
            "status": "error"
        }
    
    # Assess patient risk
    risk_assessment = assess_patient_risk(patient_id, condition, patient_info)
    
    return {
        "action": "evaluate_patient_risk",
        "patient_id": patient_id,
        "condition": condition,
        "risk_level": risk_assessment["risk_level"],
        "risk_factors": risk_assessment["risk_factors"],
        "preventive_recommendations": risk_assessment["recommendations"],
        "status": "success"
    }


def identify_abnormal_findings(vitals: Dict[str, Any], lab_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Identify abnormal findings in vitals and lab results."""
    abnormal_findings = []
    
    # Check vitals
    vital_ranges = {
        "heart_rate": {"min": 60, "max": 100},
        "blood_pressure_systolic": {"min": 90, "max": 140},
        "blood_pressure_diastolic": {"min": 60, "max": 90},
        "temperature": {"min": 36.5, "max": 37.5},
        "respiratory_rate": {"min": 12, "max": 20},
        "oxygen_saturation": {"min": 95, "max": 100}
    }
    
    for vital, value in vitals.items():
        if vital in vital_ranges:
            range_data = vital_ranges[vital]
            if value < range_data["min"] or value > range_data["max"]:
                abnormal_findings.append({
                    "type": "vital",
                    "name": vital,
                    "value": value,
                    "normal_range": f"{range_data['min']} - {range_data['max']}",
                    "severity": "high" if (value < range_data["min"] * 0.9 or value > range_data["max"] * 1.1) else "medium"
                })
    
    # Check lab results
    for lab in lab_results:
        if lab.get("result") and lab.get("reference_range"):
            result = lab["result"]
            ref_range = lab["reference_range"]
            
            # Simple parsing of reference range (assuming format like "10-20")
            try:
                min_val, max_val = map(float, ref_range.split('-'))
                if result < min_val or result > max_val:
                    abnormal_findings.append({
                        "type": "lab",
                        "name": lab.get("name", "Unknown test"),
                        "value": result,
                        "normal_range": ref_range,
                        "severity": "high" if (result < min_val * 0.8 or result > max_val * 1.2) else "medium"
                    })
            except:
                # If we can't parse the range, just note the abnormality flag
                if lab.get("is_abnormal"):
                    abnormal_findings.append({
                        "type": "lab",
                        "name": lab.get("name", "Unknown test"),
                        "value": result,
                        "normal_range": ref_range,
                        "severity": "unknown"
                    })
    
    return abnormal_findings


def calculate_confidence_scores(diagnoses: List[Dict[str, Any]], symptoms: List[str], patient_info: Dict[str, Any]) -> Dict[str, float]:
    """Calculate confidence scores for diagnoses based on symptoms and patient data."""
    confidence_scores = {}
    
    for diagnosis in diagnoses:
        # This would be a more complex algorithm in a real implementation
        # Here we're just providing a simplified example
        diagnosis_name = diagnosis["name"]
        symptom_match_score = diagnosis.get("symptom_match_percentage", 0) / 100
        risk_factor_score = diagnosis.get("risk_factor_match", 0) / 100
        
        # Calculate weighted score
        confidence_scores[diagnosis_name] = round((symptom_match_score * 0.7 + risk_factor_score * 0.3) * 100)
    
    return confidence_scores


def recommend_diagnostic_tests(diagnoses: List[Dict[str, Any]]) -> List[str]:
    """Recommend diagnostic tests based on potential diagnoses."""
    recommended_tests = set()
    
    # Map of diagnoses to recommended tests
    diagnosis_test_map = {
        "Hypertension": ["24-hour blood pressure monitoring", "Echocardiogram"],
        "Diabetes": ["Hemoglobin A1C", "Glucose tolerance test"],
        "Pneumonia": ["Chest X-ray", "Sputum culture"],
        "Anemia": ["Complete blood count", "Iron studies"],
        "Hypothyroidism": ["Thyroid function tests", "Thyroid antibodies"],
        # Add more mappings as needed
    }
    
    for diagnosis in diagnoses:
        diagnosis_name = diagnosis["name"]
        if diagnosis_name in diagnosis_test_map:
            for test in diagnosis_test_map[diagnosis_name]:
                recommended_tests.add(test)
    
    return list(recommended_tests)


def categorize_interaction_severity(interactions: List[Dict[str, Any]]) -> Dict[str, int]:
    """Categorize drug interactions by severity level."""
    severity_counts = {
        "severe": 0,
        "moderate": 0,
        "mild": 0,
        "unknown": 0
    }
    
    for interaction in interactions:
        severity = interaction.get("severity", "unknown").lower()
        if severity in severity_counts:
            severity_counts[severity] += 1
    
    return severity_counts


def generate_medication_recommendations(interactions: List[Dict[str, Any]]) -> List[str]:
    """Generate recommendations based on medication interactions."""
    recommendations = []
    
    severe_interactions = [i for i in interactions if i.get("severity") == "severe"]
    moderate_interactions = [i for i in interactions if i.get("severity") == "moderate"]
    
    # Generate recommendations for severe interactions
    for interaction in severe_interactions:
        drug1 = interaction.get("drug1", "")
        drug2 = interaction.get("drug2", "")
        recommendations.append(f"Consider alternative to {drug1} or {drug2} due to severe interaction")
    
    # Generate general recommendations
    if severe_interactions:
        recommendations.append("Consult with pharmacist immediately about severe drug interactions")
    
    if moderate_interactions:
        recommendations.append("Monitor patient closely for signs of drug interactions")
    
    if len(interactions) > 0:
        recommendations.append("Review complete medication list with patient")
    
    return recommendations


# Create the clinical data analysis agent
root_agent = Agent(
    name="clinical_data_analysis_agent",
    model="gemini-2.0-flash",
    description="Clinical data analysis agent that provides healthcare insights and clinical decision support",
    instruction="""
    You are a clinical decision support agent that analyzes medical data and provides healthcare insights.
    
    Your capabilities include:
    1. Medical record analysis and clinical insight extraction
    2. Diagnostic suggestion based on symptoms and patient data
    3. Medication interaction checking and safety analysis
    4. Patient risk assessment for various conditions
    
    When analyzing clinical data:
    - Prioritize patient safety in all recommendations
    - Highlight abnormal findings and critical values
    - Provide evidence-based recommendations
    - Consider patient demographics and risk factors
    - Maintain clinical accuracy and medical terminology
    
    Always emphasize that your analysis is for clinical decision support only and should be reviewed by qualified healthcare professionals before making treatment decisions.
    """,
    tools=[analyze_medical_record, suggest_diagnosis, check_medication_interactions, evaluate_patient_risk]
)