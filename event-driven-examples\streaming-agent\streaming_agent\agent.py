from google.adk.agents import Agent
import time
from google.adk.tools.tool_context import ToolContext


def generate_long_content(topic: str, tool_context: ToolContext) -> dict:
    """Generate long-form content with streaming simulation.
    
    Args:
        topic: The topic to generate content about
        tool_context: Context for accessing session state
        
    Returns:
        A dictionary with the generated content
    """
    print(f"--- Tool: generate_long_content called for topic '{topic}' ---")
    
    # Simulate streaming by generating content in chunks
    content_chunks = [
        f"# {topic}\n\n",
        "## Introduction\n\n",
        f"This is a comprehensive exploration of {topic}. ",
        "Let me break this down into several key areas:\n\n",
        "## Key Concepts\n\n",
        f"When discussing {topic}, it's important to understand the fundamental principles. ",
        "These concepts form the foundation of our understanding.\n\n",
        "## Detailed Analysis\n\n",
        f"The analysis of {topic} reveals several interesting patterns. ",
        "Each pattern contributes to our overall comprehension.\n\n",
        "## Practical Applications\n\n",
        f"In practice, {topic} can be applied in numerous ways. ",
        "These applications demonstrate the real-world value.\n\n",
        "## Future Considerations\n\n",
        f"Looking ahead, {topic} will continue to evolve. ",
        "Understanding these trends helps us prepare for what's coming.\n\n",
        "## Conclusion\n\n",
        f"In summary, {topic} represents a fascinating area of study ",
        "with significant implications for the future."
    ]
    
    # Store chunks in state for streaming simulation
    tool_context.state["content_chunks"] = content_chunks
    tool_context.state["current_chunk"] = 0
    
    return {
        "action": "generate_long_content",
        "topic": topic,
        "message": f"Starting to generate comprehensive content about {topic}...",
        "total_chunks": len(content_chunks)
    }


def get_next_chunk(tool_context: ToolContext) -> dict:
    """Get the next chunk of content for streaming.
    
    Args:
        tool_context: Context for accessing session state
        
    Returns:
        A dictionary with the next content chunk
    """
    print("--- Tool: get_next_chunk called ---")
    
    chunks = tool_context.state.get("content_chunks", [])
    current_chunk = tool_context.state.get("current_chunk", 0)
    
    if current_chunk >= len(chunks):
        return {
            "action": "get_next_chunk",
            "chunk": "",
            "completed": True,
            "message": "Content generation completed!"
        }
    
    chunk = chunks[current_chunk]
    tool_context.state["current_chunk"] = current_chunk + 1
    
    # Simulate processing time
    time.sleep(0.5)
    
    return {
        "action": "get_next_chunk",
        "chunk": chunk,
        "chunk_number": current_chunk + 1,
        "total_chunks": len(chunks),
        "completed": False,
        "progress": f"{current_chunk + 1}/{len(chunks)}"
    }


def simulate_progress_task(task_name: str, steps: int, tool_context: ToolContext) -> dict:
    """Simulate a long-running task with progress updates.
    
    Args:
        task_name: Name of the task to simulate
        steps: Number of steps in the task
        tool_context: Context for accessing session state
        
    Returns:
        A dictionary with task progress information
    """
    print(f"--- Tool: simulate_progress_task called for '{task_name}' with {steps} steps ---")
    
    current_step = tool_context.state.get(f"{task_name}_step", 0)
    
    if current_step >= steps:
        return {
            "action": "simulate_progress_task",
            "task_name": task_name,
            "completed": True,
            "message": f"Task '{task_name}' completed successfully!"
        }
    
    current_step += 1
    tool_context.state[f"{task_name}_step"] = current_step
    
    # Simulate work
    time.sleep(1)
    
    progress_percentage = (current_step / steps) * 100
    
    return {
        "action": "simulate_progress_task",
        "task_name": task_name,
        "current_step": current_step,
        "total_steps": steps,
        "progress_percentage": round(progress_percentage, 1),
        "completed": False,
        "message": f"Step {current_step}/{steps} completed ({progress_percentage:.1f}%)"
    }


# Create the streaming agent
root_agent = Agent(
    name="streaming_agent",
    model="gemini-2.0-flash",
    description="Agent that demonstrates streaming responses and real-time content generation",
    instruction="""
    You are a streaming agent that specializes in providing real-time, progressive responses.
    
    Your capabilities include:
    1. Generating long-form content with streaming simulation
    2. Providing progress updates for long-running tasks
    3. Breaking down responses into manageable chunks
    
    When users request long content or complex tasks:
    - Use generate_long_content for comprehensive topics
    - Use get_next_chunk to deliver content progressively
    - Use simulate_progress_task for tasks that take time
    
    Always explain to users that you're demonstrating streaming capabilities and that
    they'll see content appear progressively rather than all at once.
    
    For streaming demonstrations, suggest topics like:
    - Technical explanations
    - Long stories or narratives
    - Comprehensive guides
    - Detailed analyses
    
    Remember to be engaging and explain the streaming process as it happens.
    """,
    tools=[generate_long_content, get_next_chunk, simulate_progress_task]
)
