from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext
from datetime import datetime
import json


def generate_personalized_recommendations(customer_id: str, context: str, num_recommendations: int, tool_context: ToolContext) -> dict:
    """Generate personalized product recommendations for a customer."""
    print(f"--- Tool: generate_personalized_recommendations called for customer {customer_id} ---")
    
    # Get customer data
    customer_data = get_customer_profile(customer_id, tool_context)
    
    # Generate recommendations using hybrid approach
    collaborative_recs = collaborative_filtering(customer_id, tool_context)
    content_based_recs = content_based_filtering(customer_data, tool_context)
    behavioral_recs = behavioral_analysis(customer_data, context, tool_context)
    
    # Combine and rank recommendations
    combined_recs = combine_recommendations(collaborative_recs, content_based_recs, behavioral_recs)
    
    # Apply business rules
    final_recs = apply_business_rules(combined_recs, context, tool_context)
    
    # Return top N recommendations
    top_recommendations = final_recs[:num_recommendations]
    
    # Log recommendation event
    log_recommendation_event(customer_id, top_recommendations, context, tool_context)
    
    return {
        "action": "generate_personalized_recommendations",
        "customer_id": customer_id,
        "context": context,
        "recommendations": top_recommendations,
        "recommendation_count": len(top_recommendations),
        "generated_at": datetime.now().isoformat(),
        "status": "success"
    }


def collaborative_filtering(customer_id: str, tool_context: ToolContext) -> list:
    """Generate recommendations using collaborative filtering (customers like you also bought)."""
    
    # Simulate customer purchase data
    customer_purchases = {
        "customer_001": ["iphone_15", "airpods_pro", "iphone_case"],
        "customer_002": ["iphone_15", "magsafe_charger", "screen_protector"],
        "customer_003": ["samsung_s24", "galaxy_buds", "wireless_charger"],
        "customer_004": ["iphone_15", "airpods_pro", "apple_watch"],
        "customer_005": ["macbook_pro", "magic_mouse", "usb_c_hub"]
    }
    
    current_customer_purchases = customer_purchases.get(customer_id, [])
    
    # Find similar customers
    similar_customers = []
    for other_customer, purchases in customer_purchases.items():
        if other_customer != customer_id:
            # Calculate similarity based on common purchases
            common_items = set(current_customer_purchases) & set(purchases)
            if len(common_items) > 0:
                similarity = len(common_items) / len(set(current_customer_purchases) | set(purchases))
                similar_customers.append((other_customer, similarity, purchases))
    
    # Sort by similarity
    similar_customers.sort(key=lambda x: x[1], reverse=True)
    
    # Get recommendations from similar customers
    recommendations = []
    for similar_customer, similarity, purchases in similar_customers[:3]:
        for item in purchases:
            if item not in current_customer_purchases:
                recommendations.append({
                    "product_id": item,
                    "score": similarity * 0.8,  # Collaborative filtering weight
                    "reason": f"Customers like you also bought this",
                    "algorithm": "collaborative_filtering"
                })
    
    return recommendations


def content_based_filtering(customer_data: dict, tool_context: ToolContext) -> list:
    """Generate recommendations based on product content similarity."""
    
    # Simulate product catalog with attributes
    product_catalog = {
        "iphone_15": {"category": "smartphone", "brand": "apple", "price": 999, "features": ["5g", "camera", "ios"]},
        "iphone_case": {"category": "accessory", "brand": "apple", "price": 49, "features": ["protection", "magsafe"]},
        "airpods_pro": {"category": "audio", "brand": "apple", "price": 249, "features": ["wireless", "noise_canceling"]},
        "magsafe_charger": {"category": "accessory", "brand": "apple", "price": 39, "features": ["wireless", "charging"]},
        "samsung_s24": {"category": "smartphone", "brand": "samsung", "price": 899, "features": ["5g", "camera", "android"]},
        "macbook_pro": {"category": "laptop", "brand": "apple", "price": 1999, "features": ["m3_chip", "retina", "macos"]}
    }
    
    # Get customer's purchase history
    purchase_history = customer_data.get("purchase_history", [])
    
    recommendations = []
    
    # Find products similar to purchased items
    for purchased_item in purchase_history:
        if purchased_item in product_catalog:
            purchased_product = product_catalog[purchased_item]
            
            for product_id, product_attrs in product_catalog.items():
                if product_id not in purchase_history:
                    # Calculate content similarity
                    similarity = calculate_content_similarity(purchased_product, product_attrs)
                    
                    if similarity > 0.3:  # Threshold for relevance
                        recommendations.append({
                            "product_id": product_id,
                            "score": similarity * 0.7,  # Content-based weight
                            "reason": f"Similar to your {purchased_item}",
                            "algorithm": "content_based_filtering"
                        })
    
    return recommendations


def behavioral_analysis(customer_data: dict, context: str, tool_context: ToolContext) -> list:
    """Generate recommendations based on customer behavior patterns."""
    
    browsing_history = customer_data.get("browsing_history", [])
    search_history = customer_data.get("search_history", [])
    cart_items = customer_data.get("cart_items", [])
    
    recommendations = []
    
    # Recommendations based on browsing behavior
    for viewed_item in browsing_history[-5:]:  # Last 5 viewed items
        recommendations.append({
            "product_id": viewed_item,
            "score": 0.6,
            "reason": "You recently viewed this item",
            "algorithm": "behavioral_analysis"
        })
    
    # Recommendations based on search behavior
    for search_term in search_history[-3:]:  # Last 3 searches
        # Simulate finding products matching search terms
        matching_products = find_products_by_search(search_term)
        for product in matching_products:
            recommendations.append({
                "product_id": product,
                "score": 0.7,
                "reason": f"Matches your search for '{search_term}'",
                "algorithm": "behavioral_analysis"
            })
    
    # Cart abandonment recovery
    for cart_item in cart_items:
        recommendations.append({
            "product_id": cart_item,
            "score": 0.9,
            "reason": "Complete your purchase",
            "algorithm": "cart_recovery"
        })
    
    return recommendations


def combine_recommendations(collaborative: list, content_based: list, behavioral: list) -> list:
    """Combine recommendations from different algorithms."""
    
    # Combine all recommendations
    all_recommendations = collaborative + content_based + behavioral
    
    # Group by product_id and combine scores
    product_scores = {}
    for rec in all_recommendations:
        product_id = rec["product_id"]
        if product_id not in product_scores:
            product_scores[product_id] = {
                "product_id": product_id,
                "total_score": 0,
                "algorithms": [],
                "reasons": []
            }
        
        product_scores[product_id]["total_score"] += rec["score"]
        product_scores[product_id]["algorithms"].append(rec["algorithm"])
        product_scores[product_id]["reasons"].append(rec["reason"])
    
    # Convert to list and sort by score
    combined_recs = list(product_scores.values())
    combined_recs.sort(key=lambda x: x["total_score"], reverse=True)
    
    return combined_recs


def apply_business_rules(recommendations: list, context: str, tool_context: ToolContext) -> list:
    """Apply business rules to recommendations."""
    
    # Simulate business rules
    business_rules = {
        "inventory_boost": {"low_stock_items": ["iphone_case", "screen_protector"], "boost_factor": 1.2},
        "profit_margin": {"high_margin_items": ["airpods_pro", "magsafe_charger"], "boost_factor": 1.1},
        "seasonal_promotion": {"promoted_items": ["wireless_charger"], "boost_factor": 1.3},
        "brand_partnership": {"partner_brands": ["apple"], "boost_factor": 1.05}
    }
    
    # Apply rules
    for rec in recommendations:
        product_id = rec["product_id"]
        
        # Inventory clearance boost
        if product_id in business_rules["inventory_boost"]["low_stock_items"]:
            rec["total_score"] *= business_rules["inventory_boost"]["boost_factor"]
            rec["reasons"].append("Limited stock - order soon!")
        
        # High margin boost
        if product_id in business_rules["profit_margin"]["high_margin_items"]:
            rec["total_score"] *= business_rules["profit_margin"]["boost_factor"]
        
        # Seasonal promotion
        if product_id in business_rules["seasonal_promotion"]["promoted_items"]:
            rec["total_score"] *= business_rules["seasonal_promotion"]["boost_factor"]
            rec["reasons"].append("Special promotion - limited time!")
    
    # Re-sort after applying business rules
    recommendations.sort(key=lambda x: x["total_score"], reverse=True)
    
    # Add product details
    for rec in recommendations:
        rec.update(get_product_details(rec["product_id"]))
    
    return recommendations


def get_customer_profile(customer_id: str, tool_context: ToolContext) -> dict:
    """Get customer profile data."""
    
    # Simulate customer database
    customers = {
        "customer_001": {
            "customer_id": "customer_001",
            "purchase_history": ["iphone_15", "airpods_pro"],
            "browsing_history": ["iphone_case", "magsafe_charger", "apple_watch"],
            "search_history": ["iphone accessories", "wireless charger"],
            "cart_items": ["screen_protector"],
            "preferences": {"brand": "apple", "price_range": "premium"},
            "demographics": {"age": 28, "location": "urban"}
        },
        "customer_002": {
            "customer_id": "customer_002",
            "purchase_history": ["samsung_s24"],
            "browsing_history": ["galaxy_buds", "wireless_charger"],
            "search_history": ["android accessories"],
            "cart_items": [],
            "preferences": {"brand": "samsung", "price_range": "mid"},
            "demographics": {"age": 35, "location": "suburban"}
        }
    }
    
    return customers.get(customer_id, {
        "customer_id": customer_id,
        "purchase_history": [],
        "browsing_history": [],
        "search_history": [],
        "cart_items": [],
        "preferences": {},
        "demographics": {}
    })


def calculate_content_similarity(product1: dict, product2: dict) -> float:
    """Calculate similarity between two products based on attributes."""
    
    similarity = 0.0
    
    # Brand similarity
    if product1.get("brand") == product2.get("brand"):
        similarity += 0.3
    
    # Category similarity
    if product1.get("category") == product2.get("category"):
        similarity += 0.4
    
    # Feature similarity
    features1 = set(product1.get("features", []))
    features2 = set(product2.get("features", []))
    if features1 and features2:
        feature_similarity = len(features1 & features2) / len(features1 | features2)
        similarity += feature_similarity * 0.3
    
    return min(similarity, 1.0)


def find_products_by_search(search_term: str) -> list:
    """Find products matching search term."""
    
    # Simulate search matching
    search_matches = {
        "iphone accessories": ["iphone_case", "magsafe_charger", "screen_protector"],
        "wireless charger": ["magsafe_charger", "wireless_charger"],
        "android accessories": ["galaxy_buds", "wireless_charger"],
        "laptop": ["macbook_pro"],
        "headphones": ["airpods_pro", "galaxy_buds"]
    }
    
    return search_matches.get(search_term.lower(), [])


def get_product_details(product_id: str) -> dict:
    """Get detailed product information."""
    
    product_details = {
        "iphone_15": {"name": "iPhone 15", "price": 999, "image": "iphone15.jpg", "rating": 4.8},
        "iphone_case": {"name": "iPhone 15 Case", "price": 49, "image": "case.jpg", "rating": 4.5},
        "airpods_pro": {"name": "AirPods Pro", "price": 249, "image": "airpods.jpg", "rating": 4.7},
        "magsafe_charger": {"name": "MagSafe Charger", "price": 39, "image": "magsafe.jpg", "rating": 4.3},
        "screen_protector": {"name": "Screen Protector", "price": 19, "image": "protector.jpg", "rating": 4.2},
        "samsung_s24": {"name": "Samsung Galaxy S24", "price": 899, "image": "s24.jpg", "rating": 4.6},
        "galaxy_buds": {"name": "Galaxy Buds Pro", "price": 199, "image": "buds.jpg", "rating": 4.4},
        "wireless_charger": {"name": "Wireless Charger", "price": 29, "image": "wireless.jpg", "rating": 4.1},
        "macbook_pro": {"name": "MacBook Pro", "price": 1999, "image": "macbook.jpg", "rating": 4.9},
        "apple_watch": {"name": "Apple Watch", "price": 399, "image": "watch.jpg", "rating": 4.6}
    }
    
    return product_details.get(product_id, {"name": "Unknown Product", "price": 0, "image": "", "rating": 0})


def log_recommendation_event(customer_id: str, recommendations: list, context: str, tool_context: ToolContext) -> None:
    """Log recommendation event for analytics."""
    
    recommendation_logs = tool_context.state.get("recommendation_logs", [])
    
    log_entry = {
        "customer_id": customer_id,
        "context": context,
        "recommendations": [rec["product_id"] for rec in recommendations],
        "timestamp": datetime.now().isoformat(),
        "session_id": f"session_{hash(customer_id) % 10000}"
    }
    
    recommendation_logs.append(log_entry)
    tool_context.state["recommendation_logs"] = recommendation_logs


def track_recommendation_performance(recommendation_id: str, action: str, tool_context: ToolContext) -> dict:
    """Track how customers interact with recommendations."""
    print(f"--- Tool: track_recommendation_performance called for {recommendation_id} ---")
    
    performance_data = tool_context.state.get("recommendation_performance", {})
    
    if recommendation_id not in performance_data:
        performance_data[recommendation_id] = {
            "views": 0,
            "clicks": 0,
            "purchases": 0,
            "created_at": datetime.now().isoformat()
        }
    
    # Update metrics based on action
    if action == "view":
        performance_data[recommendation_id]["views"] += 1
    elif action == "click":
        performance_data[recommendation_id]["clicks"] += 1
    elif action == "purchase":
        performance_data[recommendation_id]["purchases"] += 1
    
    performance_data[recommendation_id]["last_action"] = datetime.now().isoformat()
    tool_context.state["recommendation_performance"] = performance_data
    
    # Calculate performance metrics
    data = performance_data[recommendation_id]
    click_through_rate = (data["clicks"] / data["views"]) * 100 if data["views"] > 0 else 0
    conversion_rate = (data["purchases"] / data["clicks"]) * 100 if data["clicks"] > 0 else 0
    
    return {
        "action": "track_recommendation_performance",
        "recommendation_id": recommendation_id,
        "tracked_action": action,
        "metrics": {
            "views": data["views"],
            "clicks": data["clicks"],
            "purchases": data["purchases"],
            "click_through_rate": round(click_through_rate, 2),
            "conversion_rate": round(conversion_rate, 2)
        },
        "status": "success"
    }


# Create the product recommendation agent
root_agent = Agent(
    name="recommendation_agent",
    model="gemini-2.0-flash",
    description="Production-ready personalized product recommendation engine using collaborative filtering, content-based filtering, and behavioral analysis",
    instruction="""
    You are a sophisticated product recommendation agent that helps e-commerce businesses increase sales through personalized recommendations.

    Current Customer Data: {customer_profiles}
    Recommendation Logs: {recommendation_logs}
    Performance Metrics: {recommendation_performance}

    Your capabilities include:
    1. Personalized product recommendations using hybrid algorithms
    2. Real-time behavioral analysis and adaptation
    3. Business rule integration for inventory and profit optimization
    4. Performance tracking and recommendation analytics

    Recommendation Strategies:
    - Collaborative Filtering: "Customers like you also bought..."
    - Content-Based: Similar products based on attributes
    - Behavioral Analysis: Based on browsing, search, and cart behavior
    - Business Rules: Inventory, profit margins, promotions

    When generating recommendations:
    1. Analyze customer purchase and browsing history
    2. Apply multiple recommendation algorithms
    3. Combine and rank results using weighted scoring
    4. Apply business rules for inventory and profit optimization
    5. Track performance for continuous improvement

    Always provide:
    - Clear reasoning for each recommendation
    - Confidence scores and relevance explanations
    - Performance tracking capabilities
    - A/B testing support for optimization

    Focus on recommendations that:
    - Match customer preferences and behavior
    - Increase average order value
    - Clear slow-moving inventory when appropriate
    - Maximize customer satisfaction and lifetime value
    """,
    tools=[generate_personalized_recommendations, track_recommendation_performance]
)
