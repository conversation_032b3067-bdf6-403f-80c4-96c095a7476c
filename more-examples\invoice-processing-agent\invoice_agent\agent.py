from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext
from datetime import datetime, timedelta
import uuid
import json
import re


def process_invoice_ocr(invoice_file: str, file_type: str, tool_context: ToolContext) -> dict:
    """Extract data from invoice using OCR technology."""
    print(f"--- Tool: process_invoice_ocr called for {file_type} file ---")
    
    # Simulate OCR processing with confidence scores
    if file_type.lower() == "pdf":
        # High-quality PDF processing
        extracted_data = {
            "vendor_name": "ABC Corporation",
            "vendor_address": "123 Business St, City, State 12345",
            "invoice_number": "INV-2024-001",
            "invoice_date": "2024-01-15",
            "due_date": "2024-02-14",
            "total_amount": 2500.00,
            "tax_amount": 200.00,
            "subtotal": 2300.00,
            "line_items": [
                {"description": "Professional Services", "quantity": 40, "rate": 50.00, "amount": 2000.00},
                {"description": "Software License", "quantity": 1, "rate": 300.00, "amount": 300.00}
            ],
            "payment_terms": "Net 30",
            "po_number": "PO-2024-456"
        }
        confidence_scores = {
            "vendor_name": 0.98,
            "invoice_number": 0.95,
            "total_amount": 0.99,
            "invoice_date": 0.97,
            "overall": 0.97
        }
    else:
        # Lower quality image processing
        extracted_data = {
            "vendor_name": "XYZ Services",
            "invoice_number": "12345",
            "invoice_date": "2024-01-10",
            "total_amount": 1200.00,
            "tax_amount": 96.00,
            "subtotal": 1104.00
        }
        confidence_scores = {
            "vendor_name": 0.85,
            "invoice_number": 0.78,
            "total_amount": 0.92,
            "invoice_date": 0.88,
            "overall": 0.86
        }
    
    # Generate processing ID
    processing_id = f"OCR-{str(uuid.uuid4())[:8].upper()}"
    
    # Store OCR results
    ocr_results = tool_context.state.get("ocr_results", {})
    ocr_results[processing_id] = {
        "processing_id": processing_id,
        "file_type": file_type,
        "extracted_data": extracted_data,
        "confidence_scores": confidence_scores,
        "processed_at": datetime.now().isoformat(),
        "status": "completed"
    }
    tool_context.state["ocr_results"] = ocr_results
    
    # Determine if manual review is needed
    manual_review_required = confidence_scores["overall"] < 0.90
    
    return {
        "action": "process_invoice_ocr",
        "processing_id": processing_id,
        "extracted_data": extracted_data,
        "confidence_scores": confidence_scores,
        "manual_review_required": manual_review_required,
        "next_step": "manual_review" if manual_review_required else "validation",
        "status": "success"
    }


def validate_invoice_data(processing_id: str, tool_context: ToolContext) -> dict:
    """Validate extracted invoice data against business rules and databases."""
    print(f"--- Tool: validate_invoice_data called for {processing_id} ---")
    
    ocr_results = tool_context.state.get("ocr_results", {})
    
    if processing_id not in ocr_results:
        return {
            "action": "validate_invoice_data",
            "error": f"OCR processing {processing_id} not found",
            "status": "error"
        }
    
    extracted_data = ocr_results[processing_id]["extracted_data"]
    
    validation_results = {
        "vendor_validation": validate_vendor(extracted_data.get("vendor_name", "")),
        "duplicate_check": check_duplicate_invoice(extracted_data, tool_context),
        "po_validation": validate_purchase_order(extracted_data.get("po_number", ""), extracted_data),
        "amount_validation": validate_amounts(extracted_data),
        "date_validation": validate_dates(extracted_data),
        "tax_validation": validate_tax_calculation(extracted_data)
    }
    
    # Determine overall validation status
    validation_issues = []
    for check_name, result in validation_results.items():
        if not result["valid"]:
            validation_issues.append(f"{check_name}: {result['message']}")
    
    validation_passed = len(validation_issues) == 0
    
    # Store validation results
    validations = tool_context.state.get("invoice_validations", {})
    validations[processing_id] = {
        "processing_id": processing_id,
        "validation_results": validation_results,
        "validation_passed": validation_passed,
        "validation_issues": validation_issues,
        "validated_at": datetime.now().isoformat(),
        "next_step": "approval" if validation_passed else "exception_handling"
    }
    tool_context.state["invoice_validations"] = validations
    
    return {
        "action": "validate_invoice_data",
        "processing_id": processing_id,
        "validation_passed": validation_passed,
        "validation_results": validation_results,
        "validation_issues": validation_issues,
        "next_step": "approval" if validation_passed else "exception_handling",
        "status": "success"
    }


def route_for_approval(processing_id: str, tool_context: ToolContext) -> dict:
    """Route invoice for approval based on amount and business rules."""
    print(f"--- Tool: route_for_approval called for {processing_id} ---")
    
    ocr_results = tool_context.state.get("ocr_results", {})
    validations = tool_context.state.get("invoice_validations", {})
    
    if processing_id not in ocr_results or processing_id not in validations:
        return {
            "action": "route_for_approval",
            "error": f"Processing data not found for {processing_id}",
            "status": "error"
        }
    
    extracted_data = ocr_results[processing_id]["extracted_data"]
    validation_data = validations[processing_id]
    
    if not validation_data["validation_passed"]:
        return {
            "action": "route_for_approval",
            "error": "Cannot route for approval - validation failed",
            "validation_issues": validation_data["validation_issues"],
            "status": "error"
        }
    
    amount = extracted_data.get("total_amount", 0)
    vendor_name = extracted_data.get("vendor_name", "")
    
    # Determine approval routing based on amount and rules
    approval_routing = determine_approval_routing(amount, vendor_name, extracted_data)
    
    # Create approval request
    approval_requests = tool_context.state.get("approval_requests", {})
    approval_id = f"APR-{str(uuid.uuid4())[:8].upper()}"
    
    approval_requests[approval_id] = {
        "approval_id": approval_id,
        "processing_id": processing_id,
        "invoice_data": extracted_data,
        "approval_routing": approval_routing,
        "current_approver": approval_routing["approvers"][0] if approval_routing["approvers"] else None,
        "approval_level": 1,
        "status": "pending_approval",
        "created_at": datetime.now().isoformat(),
        "due_date": (datetime.now() + timedelta(days=approval_routing["sla_days"])).isoformat()
    }
    tool_context.state["approval_requests"] = approval_requests
    
    # Send approval notifications
    notifications = send_approval_notifications(approval_id, approval_routing, extracted_data)
    
    return {
        "action": "route_for_approval",
        "processing_id": processing_id,
        "approval_id": approval_id,
        "approval_routing": approval_routing,
        "current_approver": approval_routing["approvers"][0] if approval_routing["approvers"] else None,
        "notifications_sent": notifications,
        "sla_due_date": approval_requests[approval_id]["due_date"],
        "status": "success"
    }


def validate_vendor(vendor_name: str) -> dict:
    """Validate vendor against approved vendor database."""
    # Simulate vendor database
    approved_vendors = [
        "ABC Corporation", "XYZ Services", "Tech Solutions Inc", 
        "Professional Services LLC", "Global Consulting"
    ]
    
    vendor_found = vendor_name in approved_vendors
    
    return {
        "valid": vendor_found,
        "message": "Vendor verified in approved vendor list" if vendor_found else f"Vendor '{vendor_name}' not found in approved list",
        "vendor_status": "approved" if vendor_found else "requires_approval"
    }


def check_duplicate_invoice(invoice_data: dict, tool_context: ToolContext) -> dict:
    """Check for duplicate invoices."""
    invoice_number = invoice_data.get("invoice_number", "")
    vendor_name = invoice_data.get("vendor_name", "")
    amount = invoice_data.get("total_amount", 0)
    
    # Check against processed invoices
    ocr_results = tool_context.state.get("ocr_results", {})
    
    for result in ocr_results.values():
        existing_data = result["extracted_data"]
        if (existing_data.get("invoice_number") == invoice_number and
            existing_data.get("vendor_name") == vendor_name and
            abs(existing_data.get("total_amount", 0) - amount) < 0.01):
            return {
                "valid": False,
                "message": f"Duplicate invoice detected: {invoice_number} from {vendor_name}",
                "duplicate_found": True
            }
    
    return {
        "valid": True,
        "message": "No duplicate invoice found",
        "duplicate_found": False
    }


def validate_purchase_order(po_number: str, invoice_data: dict) -> dict:
    """Validate invoice against purchase order (three-way matching)."""
    if not po_number:
        return {
            "valid": True,  # Non-PO invoices are allowed
            "message": "No PO number provided - non-PO invoice",
            "po_required": False
        }
    
    # Simulate PO database
    purchase_orders = {
        "PO-2024-456": {
            "vendor": "ABC Corporation",
            "amount": 2500.00,
            "status": "open",
            "items": ["Professional Services", "Software License"]
        },
        "PO-2024-789": {
            "vendor": "XYZ Services",
            "amount": 1500.00,
            "status": "open",
            "items": ["Consulting Services"]
        }
    }
    
    po_data = purchase_orders.get(po_number)
    
    if not po_data:
        return {
            "valid": False,
            "message": f"Purchase order {po_number} not found",
            "po_found": False
        }
    
    # Validate vendor match
    if po_data["vendor"] != invoice_data.get("vendor_name"):
        return {
            "valid": False,
            "message": f"Vendor mismatch: PO vendor '{po_data['vendor']}' vs Invoice vendor '{invoice_data.get('vendor_name')}'",
            "vendor_match": False
        }
    
    # Validate amount (allow 5% tolerance)
    po_amount = po_data["amount"]
    invoice_amount = invoice_data.get("total_amount", 0)
    amount_variance = abs(po_amount - invoice_amount) / po_amount
    
    if amount_variance > 0.05:  # 5% tolerance
        return {
            "valid": False,
            "message": f"Amount variance too high: PO ${po_amount} vs Invoice ${invoice_amount} ({amount_variance:.1%})",
            "amount_match": False
        }
    
    return {
        "valid": True,
        "message": "PO validation passed - three-way match successful",
        "po_found": True,
        "vendor_match": True,
        "amount_match": True
    }


def validate_amounts(invoice_data: dict) -> dict:
    """Validate invoice amount calculations."""
    subtotal = invoice_data.get("subtotal", 0)
    tax_amount = invoice_data.get("tax_amount", 0)
    total_amount = invoice_data.get("total_amount", 0)
    
    # Check if subtotal + tax = total (with small tolerance for rounding)
    calculated_total = subtotal + tax_amount
    variance = abs(calculated_total - total_amount)
    
    if variance > 0.02:  # 2 cent tolerance
        return {
            "valid": False,
            "message": f"Amount calculation error: Subtotal ${subtotal} + Tax ${tax_amount} = ${calculated_total}, but Total shows ${total_amount}",
            "calculation_error": True
        }
    
    # Check for reasonable amounts
    if total_amount <= 0:
        return {
            "valid": False,
            "message": "Invalid total amount: must be greater than zero",
            "amount_error": True
        }
    
    return {
        "valid": True,
        "message": "Amount validation passed",
        "calculation_correct": True
    }


def validate_dates(invoice_data: dict) -> dict:
    """Validate invoice dates."""
    try:
        invoice_date = datetime.fromisoformat(invoice_data.get("invoice_date", ""))
        due_date = datetime.fromisoformat(invoice_data.get("due_date", "")) if invoice_data.get("due_date") else None
        
        # Check if invoice date is not in the future
        if invoice_date > datetime.now():
            return {
                "valid": False,
                "message": "Invoice date cannot be in the future",
                "date_error": True
            }
        
        # Check if invoice date is not too old (e.g., more than 1 year)
        if invoice_date < datetime.now() - timedelta(days=365):
            return {
                "valid": False,
                "message": "Invoice date is more than 1 year old",
                "date_error": True
            }
        
        # Check if due date is after invoice date
        if due_date and due_date < invoice_date:
            return {
                "valid": False,
                "message": "Due date cannot be before invoice date",
                "date_error": True
            }
        
        return {
            "valid": True,
            "message": "Date validation passed",
            "dates_valid": True
        }
        
    except ValueError:
        return {
            "valid": False,
            "message": "Invalid date format",
            "date_format_error": True
        }


def validate_tax_calculation(invoice_data: dict) -> dict:
    """Validate tax calculation."""
    subtotal = invoice_data.get("subtotal", 0)
    tax_amount = invoice_data.get("tax_amount", 0)
    
    if subtotal == 0:
        return {
            "valid": True,
            "message": "No subtotal to validate tax against",
            "tax_validation": "skipped"
        }
    
    # Assume 8% tax rate (this would be configurable in real implementation)
    expected_tax = subtotal * 0.08
    tax_variance = abs(expected_tax - tax_amount) / expected_tax if expected_tax > 0 else 0
    
    if tax_variance > 0.1:  # 10% tolerance for different tax rates
        return {
            "valid": False,
            "message": f"Tax calculation may be incorrect: Expected ~${expected_tax:.2f}, Got ${tax_amount}",
            "tax_calculation_error": True
        }
    
    return {
        "valid": True,
        "message": "Tax calculation appears correct",
        "tax_valid": True
    }


def determine_approval_routing(amount: float, vendor: str, invoice_data: dict) -> dict:
    """Determine approval routing based on business rules."""
    
    # Define approval thresholds and routing
    if amount >= 10000:
        return {
            "approval_type": "executive",
            "approvers": ["CFO", "CEO"],
            "approval_levels": 2,
            "sla_days": 5,
            "auto_approve": False,
            "reason": f"Amount ${amount} requires executive approval"
        }
    elif amount >= 5000:
        return {
            "approval_type": "manager",
            "approvers": ["Department Manager", "Finance Manager"],
            "approval_levels": 2,
            "sla_days": 3,
            "auto_approve": False,
            "reason": f"Amount ${amount} requires manager approval"
        }
    elif amount >= 1000:
        return {
            "approval_type": "supervisor",
            "approvers": ["Supervisor"],
            "approval_levels": 1,
            "sla_days": 2,
            "auto_approve": False,
            "reason": f"Amount ${amount} requires supervisor approval"
        }
    else:
        return {
            "approval_type": "auto",
            "approvers": [],
            "approval_levels": 0,
            "sla_days": 1,
            "auto_approve": True,
            "reason": f"Amount ${amount} is under auto-approval threshold"
        }


def send_approval_notifications(approval_id: str, routing: dict, invoice_data: dict) -> list:
    """Send approval notifications to relevant parties."""
    notifications = []
    
    if routing["auto_approve"]:
        notifications.append({
            "type": "auto_approval",
            "recipient": "system",
            "message": f"Invoice {invoice_data.get('invoice_number')} auto-approved for ${invoice_data.get('total_amount')}",
            "sent_at": datetime.now().isoformat()
        })
    else:
        for approver in routing["approvers"]:
            notifications.append({
                "type": "approval_request",
                "recipient": approver,
                "message": f"Invoice approval required: {invoice_data.get('vendor_name')} - ${invoice_data.get('total_amount')} (ID: {approval_id})",
                "sent_at": datetime.now().isoformat()
            })
    
    return notifications


# Create the invoice processing agent
root_agent = Agent(
    name="invoice_agent",
    model="gemini-2.0-flash",
    description="Production-ready automated invoice processing system with OCR, validation, and approval workflows",
    instruction="""
    You are a sophisticated invoice processing agent that automates accounts payable operations for enterprises.

    Current Processing Data: {ocr_results}
    Validation Results: {invoice_validations}
    Approval Requests: {approval_requests}

    Your capabilities include:
    1. OCR data extraction from invoice documents with confidence scoring
    2. Comprehensive validation against business rules and databases
    3. Automated approval routing based on amount thresholds and policies
    4. Integration with ERP systems and vendor databases

    Invoice Processing Workflow:
    1. OCR Processing: Extract structured data from invoice documents
    2. Data Validation: Verify vendor, check duplicates, validate PO matching
    3. Approval Routing: Route based on amount thresholds and business rules
    4. ERP Integration: Post approved invoices to accounting systems

    Validation Checks:
    - Vendor verification against approved vendor list
    - Duplicate invoice detection and prevention
    - Purchase order three-way matching (PO, receipt, invoice)
    - Amount calculation verification (subtotal + tax = total)
    - Date validation (reasonable invoice and due dates)
    - Tax calculation verification

    Approval Routing Rules:
    - $10,000+: Executive approval (CFO + CEO)
    - $5,000-$9,999: Manager approval (Department + Finance)
    - $1,000-$4,999: Supervisor approval
    - Under $1,000: Auto-approval

    When processing invoices:
    1. Always check OCR confidence scores and flag low-confidence extractions
    2. Run comprehensive validation before approval routing
    3. Apply appropriate approval routing based on amount and vendor
    4. Provide clear status updates and exception handling
    5. Maintain audit trails for compliance

    Focus on:
    - Accuracy and data quality in extraction and validation
    - Compliance with financial controls and approval policies
    - Efficient processing while maintaining proper controls
    - Clear exception handling and manual review processes
    - Integration readiness with ERP and accounting systems
    """,
    tools=[process_invoice_ocr, validate_invoice_data, route_for_approval]
)
