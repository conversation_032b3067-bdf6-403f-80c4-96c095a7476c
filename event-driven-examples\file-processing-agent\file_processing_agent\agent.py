from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext
import json
import csv
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import Dict, Any, List
import base64
import mimetypes
import os


def upload_file(filename: str, file_content: str, file_type: str, tool_context: ToolContext) -> dict:
    """Upload and validate a file for processing.
    
    Args:
        filename: Name of the uploaded file
        file_content: Base64 encoded file content or text content
        file_type: MIME type of the file
        tool_context: Context for accessing session state
        
    Returns:
        A dictionary with upload confirmation and file metadata
    """
    print(f"--- Tool: upload_file called for {filename} ({file_type}) ---")
    
    # Validate file type
    allowed_types = [
        'text/plain', 'text/csv', 'application/json', 'application/xml',
        'text/xml', 'application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]
    
    if file_type not in allowed_types:
        return {
            "action": "upload_file",
            "error": f"File type {file_type} not supported",
            "supported_types": allowed_types,
            "status": "error"
        }
    
    # Get file size (approximate for base64)
    file_size = len(file_content) * 3 // 4 if file_content.startswith('data:') else len(file_content)
    
    # Validate file size (10MB limit)
    max_size = 10 * 1024 * 1024  # 10MB
    if file_size > max_size:
        return {
            "action": "upload_file",
            "error": f"File size ({file_size} bytes) exceeds maximum allowed size ({max_size} bytes)",
            "status": "error"
        }
    
    # Store file metadata
    uploaded_files = tool_context.state.get("uploaded_files", {})
    file_id = f"file_{len(uploaded_files) + 1}_{hash(filename) % 10000}"
    
    uploaded_files[file_id] = {
        "filename": filename,
        "file_type": file_type,
        "file_size": file_size,
        "uploaded_at": datetime.now().isoformat(),
        "content": file_content,
        "processed": False,
        "status": "uploaded"
    }
    
    tool_context.state["uploaded_files"] = uploaded_files
    
    return {
        "action": "upload_file",
        "file_id": file_id,
        "filename": filename,
        "file_type": file_type,
        "file_size": file_size,
        "message": f"File {filename} uploaded successfully",
        "status": "success"
    }


def process_file(file_id: str, processing_type: str, tool_context: ToolContext) -> dict:
    """Process an uploaded file based on the specified processing type.
    
    Args:
        file_id: ID of the uploaded file
        processing_type: Type of processing ('extract', 'analyze', 'convert', 'summarize')
        tool_context: Context for accessing session state
        
    Returns:
        A dictionary with processing results
    """
    print(f"--- Tool: process_file called for {file_id} with {processing_type} ---")
    
    uploaded_files = tool_context.state.get("uploaded_files", {})
    
    if file_id not in uploaded_files:
        return {
            "action": "process_file",
            "error": f"File with ID {file_id} not found",
            "status": "error"
        }
    
    file_info = uploaded_files[file_id]
    
    try:
        if processing_type == "extract":
            result = extract_file_content(file_info)
        elif processing_type == "analyze":
            result = analyze_file_content(file_info)
        elif processing_type == "convert":
            result = convert_file_format(file_info)
        elif processing_type == "summarize":
            result = summarize_file_content(file_info)
        else:
            return {
                "action": "process_file",
                "error": f"Unknown processing type: {processing_type}",
                "status": "error"
            }
        
        # Update file status
        file_info["processed"] = True
        file_info["last_processed"] = datetime.now().isoformat()
        file_info["processing_type"] = processing_type
        uploaded_files[file_id] = file_info
        tool_context.state["uploaded_files"] = uploaded_files
        
        return {
            "action": "process_file",
            "file_id": file_id,
            "filename": file_info["filename"],
            "processing_type": processing_type,
            "result": result,
            "processed_at": datetime.now().isoformat(),
            "status": "success"
        }
        
    except Exception as e:
        return {
            "action": "process_file",
            "error": f"Processing error: {str(e)}",
            "status": "error"
        }


def extract_file_content(file_info: Dict[str, Any]) -> dict:
    """Extract content from a file based on its type."""
    file_type = file_info["file_type"]
    content = file_info["content"]
    
    if file_type == "text/plain":
        return {
            "content_type": "text",
            "extracted_text": content,
            "word_count": len(content.split()),
            "character_count": len(content)
        }
    
    elif file_type == "text/csv":
        try:
            lines = content.strip().split('\n')
            reader = csv.DictReader(lines)
            data = list(reader)
            return {
                "content_type": "csv",
                "row_count": len(data),
                "columns": list(data[0].keys()) if data else [],
                "sample_data": data[:5] if data else []
            }
        except Exception as e:
            return {"error": f"CSV parsing error: {str(e)}"}
    
    elif file_type == "application/json":
        try:
            data = json.loads(content)
            return {
                "content_type": "json",
                "data_type": type(data).__name__,
                "keys": list(data.keys()) if isinstance(data, dict) else None,
                "length": len(data) if isinstance(data, (list, dict)) else None,
                "sample_data": str(data)[:500] + "..." if len(str(data)) > 500 else str(data)
            }
        except Exception as e:
            return {"error": f"JSON parsing error: {str(e)}"}
    
    elif file_type in ["application/xml", "text/xml"]:
        try:
            root = ET.fromstring(content)
            return {
                "content_type": "xml",
                "root_tag": root.tag,
                "child_count": len(root),
                "attributes": root.attrib,
                "text_content": root.text[:500] + "..." if root.text and len(root.text) > 500 else root.text
            }
        except Exception as e:
            return {"error": f"XML parsing error: {str(e)}"}
    
    else:
        return {
            "content_type": "binary",
            "message": f"Binary file type {file_type} - content extraction not implemented",
            "file_size": len(content)
        }


def analyze_file_content(file_info: Dict[str, Any]) -> dict:
    """Analyze file content and provide insights."""
    extraction_result = extract_file_content(file_info)
    
    if "error" in extraction_result:
        return extraction_result
    
    content_type = extraction_result.get("content_type")
    
    if content_type == "text":
        text = extraction_result["extracted_text"]
        words = text.split()
        sentences = text.split('.')
        
        return {
            "analysis_type": "text_analysis",
            "word_count": len(words),
            "sentence_count": len(sentences),
            "average_word_length": sum(len(word) for word in words) / len(words) if words else 0,
            "most_common_words": get_word_frequency(words)[:10],
            "readability_score": calculate_readability_score(text)
        }
    
    elif content_type == "csv":
        return {
            "analysis_type": "data_analysis",
            "structure": extraction_result,
            "data_quality": "Good" if extraction_result.get("row_count", 0) > 0 else "Poor",
            "completeness": "Complete" if extraction_result.get("columns") else "Incomplete"
        }
    
    elif content_type == "json":
        return {
            "analysis_type": "json_analysis",
            "structure": extraction_result,
            "complexity": "High" if extraction_result.get("length", 0) > 100 else "Low"
        }
    
    else:
        return {
            "analysis_type": "general",
            "message": f"Basic analysis for {content_type} files",
            "file_info": extraction_result
        }


def get_word_frequency(words: List[str]) -> List[tuple]:
    """Get word frequency from a list of words."""
    word_count = {}
    for word in words:
        clean_word = word.lower().strip('.,!?";')
        if len(clean_word) > 2:  # Ignore short words
            word_count[clean_word] = word_count.get(clean_word, 0) + 1
    
    return sorted(word_count.items(), key=lambda x: x[1], reverse=True)


def calculate_readability_score(text: str) -> float:
    """Calculate a simple readability score."""
    words = text.split()
    sentences = text.split('.')
    
    if not words or not sentences:
        return 0.0
    
    avg_sentence_length = len(words) / len(sentences)
    avg_word_length = sum(len(word) for word in words) / len(words)
    
    # Simple readability formula (lower is easier to read)
    score = (avg_sentence_length * 0.5) + (avg_word_length * 2.0)
    return round(score, 2)


def convert_file_format(file_info: Dict[str, Any]) -> dict:
    """Convert file to different formats (simulation)."""
    file_type = file_info["file_type"]
    filename = file_info["filename"]
    
    # Simulate format conversion capabilities
    conversion_options = {
        "text/plain": ["json", "xml", "csv"],
        "text/csv": ["json", "xml", "txt"],
        "application/json": ["csv", "xml", "txt"],
        "application/xml": ["json", "csv", "txt"]
    }
    
    available_formats = conversion_options.get(file_type, [])
    
    return {
        "conversion_type": "format_conversion",
        "source_format": file_type,
        "available_formats": available_formats,
        "message": f"File {filename} can be converted to: {', '.join(available_formats)}",
        "note": "This is a simulation - actual conversion would require format-specific libraries"
    }


def summarize_file_content(file_info: Dict[str, Any]) -> dict:
    """Generate a summary of file content."""
    extraction_result = extract_file_content(file_info)
    
    if "error" in extraction_result:
        return extraction_result
    
    content_type = extraction_result.get("content_type")
    filename = file_info["filename"]
    
    if content_type == "text":
        text = extraction_result["extracted_text"]
        sentences = text.split('.')[:3]  # First 3 sentences as summary
        
        return {
            "summary_type": "text_summary",
            "filename": filename,
            "summary": '. '.join(sentences) + '.' if sentences else "No content to summarize",
            "word_count": extraction_result["word_count"],
            "key_stats": f"Document contains {extraction_result['word_count']} words and {extraction_result['character_count']} characters"
        }
    
    elif content_type == "csv":
        return {
            "summary_type": "data_summary",
            "filename": filename,
            "summary": f"CSV file with {extraction_result['row_count']} rows and {len(extraction_result['columns'])} columns",
            "columns": extraction_result["columns"],
            "data_preview": extraction_result.get("sample_data", [])
        }
    
    else:
        return {
            "summary_type": "general_summary",
            "filename": filename,
            "summary": f"File of type {content_type} processed successfully",
            "details": extraction_result
        }


def list_uploaded_files(tool_context: ToolContext) -> dict:
    """List all uploaded files and their status.
    
    Args:
        tool_context: Context for accessing session state
        
    Returns:
        A dictionary with all uploaded files
    """
    print("--- Tool: list_uploaded_files called ---")
    
    uploaded_files = tool_context.state.get("uploaded_files", {})
    
    # Create summary without file content
    file_summary = {}
    for file_id, file_info in uploaded_files.items():
        file_summary[file_id] = {
            "filename": file_info["filename"],
            "file_type": file_info["file_type"],
            "file_size": file_info["file_size"],
            "uploaded_at": file_info["uploaded_at"],
            "processed": file_info["processed"],
            "status": file_info["status"]
        }
    
    return {
        "action": "list_uploaded_files",
        "files": file_summary,
        "total_files": len(uploaded_files)
    }


# Create the file processing agent
root_agent = Agent(
    name="file_processing_agent",
    model="gemini-2.0-flash",
    description="Agent that handles file upload, processing, and manipulation for document-based workflows",
    instruction="""
    You are a file processing agent that specializes in handling various file formats and extracting insights from documents.
    
    Your capabilities include:
    1. File upload and validation (PDF, DOCX, TXT, CSV, JSON, XML)
    2. Content extraction from different file formats
    3. File analysis and insight generation
    4. Format conversion between supported types
    5. Content summarization and reporting
    6. Batch file processing
    
    Supported file types:
    - Text files: TXT, CSV
    - Data files: JSON, XML
    - Documents: PDF, DOCX (basic support)
    
    When users want to work with files:
    - Use upload_file to accept and validate file uploads
    - Use process_file with different processing types:
      * 'extract' - Extract raw content and metadata
      * 'analyze' - Perform content analysis and insights
      * 'convert' - Show available format conversions
      * 'summarize' - Generate content summaries
    - Use list_uploaded_files to show all uploaded files
    
    Always emphasize security and validation:
    - Check file types and sizes
    - Validate file content before processing
    - Handle errors gracefully
    - Provide clear feedback on processing results
    
    For demonstrations, you can simulate file uploads with sample content.
    """,
    tools=[upload_file, process_file, list_uploaded_files]
)
