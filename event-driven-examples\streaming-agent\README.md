# Streaming Agent

This example demonstrates how to implement streaming responses in the Agent Development Kit (ADK) for real-time user interaction and better user experience with long-running tasks.

## Overview

The Streaming Agent showcases:
1. Real-time response streaming for immediate feedback
2. Progress updates during long-running operations
3. Chunked content delivery for better perceived performance
4. Handling of streaming interruptions and errors

## What is Streaming?

Streaming in ADK allows agents to:

1. **Provide Immediate Feedback**: Start showing responses as soon as they're generated
2. **Improve User Experience**: Users see progress instead of waiting for complete responses
3. **Handle Long Content**: Break down lengthy responses into manageable chunks
4. **Enable Real-time Interaction**: Create more conversational and responsive experiences

## Key Features

### Real-time Response Streaming
The agent demonstrates how to stream responses token by token, providing immediate visual feedback to users.

### Progress Indicators
Shows how to implement progress updates for long-running tasks like content generation or data processing.

### Error Handling
Includes proper error handling for streaming interruptions and network issues.

## Project Structure

```
13-streaming-agent/
├── README.md
└── streaming_agent/
    ├── __init__.py
    └── agent.py
```

## How It Works

The streaming agent uses ADK's built-in streaming capabilities to:

1. **Initialize Streaming**: Set up the streaming configuration
2. **Generate Content**: Create content in chunks or tokens
3. **Stream Responses**: Send partial responses as they're generated
4. **Handle Completion**: Properly close the streaming session

## Usage

To run this example:

```bash
cd 13-streaming-agent
adk web
```

Then in the web interface, try prompts like:
- "Write a long story about space exploration"
- "Generate a detailed technical explanation of machine learning"
- "Create a comprehensive business plan"

You'll see the responses appear in real-time as they're generated, rather than waiting for the complete response.

## Key Concepts

### Streaming Configuration
Learn how to configure agents for streaming responses and set appropriate buffer sizes.

### Chunk Management
Understand how to break content into appropriate chunks for optimal streaming performance.

### User Experience
See how streaming improves the perceived performance and responsiveness of your agents.

## Best Practices

1. **Use for Long Content**: Streaming is most beneficial for lengthy responses
2. **Handle Interruptions**: Always implement proper error handling for streaming
3. **Optimize Chunk Size**: Balance between responsiveness and efficiency
4. **Provide Progress Feedback**: Keep users informed about the streaming progress

This example provides a foundation for building responsive, real-time AI applications with ADK.
