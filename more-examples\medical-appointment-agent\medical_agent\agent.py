from google.adk.agents import Agent
from google.adk.tools.tool_context import <PERSON>lContext
from datetime import datetime, timedelta
import uuid
import json


def schedule_medical_appointment(patient_id: str, appointment_type: str, preferred_provider: str, preferred_times: str, tool_context: ToolContext) -> dict:
    """Schedule a medical appointment with provider availability and insurance verification."""
    print(f"--- Tool: schedule_medical_appointment called for patient {patient_id} ---")
    
    # Get patient information
    patient_data = get_patient_data(patient_id, tool_context)
    if not patient_data:
        return {
            "action": "schedule_medical_appointment",
            "error": f"Patient {patient_id} not found",
            "status": "error"
        }
    
    # Verify insurance eligibility
    insurance_verification = verify_insurance_eligibility(patient_data["insurance"], appointment_type)
    
    if not insurance_verification["eligible"]:
        return {
            "action": "schedule_medical_appointment",
            "error": f"Insurance verification failed: {insurance_verification['reason']}",
            "insurance_status": insurance_verification,
            "status": "error"
        }
    
    # Parse preferred times
    try:
        time_preferences = json.loads(preferred_times) if preferred_times else {}
    except json.JSONDecodeError:
        time_preferences = {}
    
    # Find available appointment slots
    available_slots = find_available_slots(appointment_type, preferred_provider, time_preferences, tool_context)
    
    if not available_slots:
        return {
            "action": "schedule_medical_appointment",
            "error": "No available appointment slots found",
            "recommendations": suggest_alternative_options(appointment_type, preferred_provider),
            "status": "error"
        }
    
    # Select best slot based on preferences
    selected_slot = select_optimal_slot(available_slots, time_preferences, patient_data)
    
    # Create appointment
    appointment_id = f"APT-{str(uuid.uuid4())[:8].upper()}"
    appointment = {
        "appointment_id": appointment_id,
        "patient_id": patient_id,
        "patient_name": patient_data["name"],
        "provider_id": selected_slot["provider_id"],
        "provider_name": selected_slot["provider_name"],
        "appointment_type": appointment_type,
        "scheduled_datetime": selected_slot["datetime"],
        "duration_minutes": selected_slot["duration"],
        "location": selected_slot["location"],
        "room": selected_slot.get("room", "TBD"),
        "status": "scheduled",
        "insurance_info": insurance_verification,
        "copay_amount": insurance_verification["copay"],
        "created_at": datetime.now().isoformat(),
        "special_instructions": get_appointment_instructions(appointment_type),
        "preparation_required": get_preparation_requirements(appointment_type)
    }
    
    # Store appointment
    appointments = tool_context.state.get("medical_appointments", {})
    appointments[appointment_id] = appointment
    tool_context.state["medical_appointments"] = appointments
    
    # Send confirmations and reminders
    notifications = send_appointment_notifications(appointment, "scheduled")
    
    # Update provider calendar
    update_provider_calendar(selected_slot["provider_id"], selected_slot, "booked")
    
    return {
        "action": "schedule_medical_appointment",
        "appointment_id": appointment_id,
        "patient_name": patient_data["name"],
        "provider": selected_slot["provider_name"],
        "scheduled_datetime": selected_slot["datetime"],
        "location": selected_slot["location"],
        "appointment_type": appointment_type,
        "copay_amount": insurance_verification["copay"],
        "preparation_instructions": appointment["preparation_required"],
        "notifications_sent": notifications,
        "status": "success"
    }


def verify_insurance_coverage(patient_id: str, procedure_code: str, tool_context: ToolContext) -> dict:
    """Verify insurance coverage for specific medical procedures."""
    print(f"--- Tool: verify_insurance_coverage called for patient {patient_id}, procedure {procedure_code} ---")
    
    patient_data = get_patient_data(patient_id, tool_context)
    if not patient_data:
        return {
            "action": "verify_insurance_coverage",
            "error": f"Patient {patient_id} not found",
            "status": "error"
        }
    
    insurance_info = patient_data.get("insurance", {})
    
    # Simulate real-time insurance verification
    coverage_result = check_procedure_coverage(insurance_info, procedure_code)
    
    # Check for prior authorization requirements
    prior_auth_required = check_prior_authorization_requirement(procedure_code, insurance_info)
    
    # Calculate patient responsibility
    cost_breakdown = calculate_patient_costs(procedure_code, insurance_info, coverage_result)
    
    verification_id = f"VER-{str(uuid.uuid4())[:8].upper()}"
    
    # Store verification result
    verifications = tool_context.state.get("insurance_verifications", {})
    verifications[verification_id] = {
        "verification_id": verification_id,
        "patient_id": patient_id,
        "procedure_code": procedure_code,
        "insurance_plan": insurance_info.get("plan_name", "Unknown"),
        "coverage_result": coverage_result,
        "prior_auth_required": prior_auth_required,
        "cost_breakdown": cost_breakdown,
        "verified_at": datetime.now().isoformat(),
        "valid_until": (datetime.now() + timedelta(days=30)).isoformat()
    }
    tool_context.state["insurance_verifications"] = verifications
    
    return {
        "action": "verify_insurance_coverage",
        "verification_id": verification_id,
        "patient_id": patient_id,
        "procedure_code": procedure_code,
        "coverage_status": coverage_result["status"],
        "coverage_percentage": coverage_result["coverage_percentage"],
        "prior_authorization_required": prior_auth_required["required"],
        "estimated_patient_cost": cost_breakdown["patient_responsibility"],
        "cost_breakdown": cost_breakdown,
        "verification_valid_until": verifications[verification_id]["valid_until"],
        "status": "success"
    }


def send_appointment_reminders(appointment_id: str, reminder_type: str, tool_context: ToolContext) -> dict:
    """Send automated appointment reminders to patients."""
    print(f"--- Tool: send_appointment_reminders called for {appointment_id}, type: {reminder_type} ---")
    
    appointments = tool_context.state.get("medical_appointments", {})
    
    if appointment_id not in appointments:
        return {
            "action": "send_appointment_reminders",
            "error": f"Appointment {appointment_id} not found",
            "status": "error"
        }
    
    appointment = appointments[appointment_id]
    patient_data = get_patient_data(appointment["patient_id"], tool_context)
    
    # Determine reminder timing and content
    reminder_config = get_reminder_configuration(reminder_type, appointment["appointment_type"])
    
    # Generate reminder content
    reminder_content = generate_reminder_content(appointment, patient_data, reminder_type)
    
    # Send reminders via multiple channels
    sent_reminders = []
    
    # SMS Reminder
    if patient_data.get("phone") and reminder_config["send_sms"]:
        sms_result = send_sms_reminder(patient_data["phone"], reminder_content["sms"])
        sent_reminders.append({
            "channel": "sms",
            "recipient": patient_data["phone"],
            "content": reminder_content["sms"],
            "sent_at": datetime.now().isoformat(),
            "status": sms_result["status"]
        })
    
    # Email Reminder
    if patient_data.get("email") and reminder_config["send_email"]:
        email_result = send_email_reminder(patient_data["email"], reminder_content["email"])
        sent_reminders.append({
            "channel": "email",
            "recipient": patient_data["email"],
            "subject": reminder_content["email"]["subject"],
            "sent_at": datetime.now().isoformat(),
            "status": email_result["status"]
        })
    
    # Phone Call Reminder (for high-priority appointments)
    if reminder_config["send_call"] and appointment["appointment_type"] in ["surgery", "procedure"]:
        call_result = schedule_phone_reminder(patient_data["phone"], reminder_content["call"])
        sent_reminders.append({
            "channel": "phone_call",
            "recipient": patient_data["phone"],
            "scheduled_time": call_result["scheduled_time"],
            "status": call_result["status"]
        })
    
    # Update appointment with reminder history
    if "reminders_sent" not in appointment:
        appointment["reminders_sent"] = []
    
    appointment["reminders_sent"].extend(sent_reminders)
    appointments[appointment_id] = appointment
    tool_context.state["medical_appointments"] = appointments
    
    return {
        "action": "send_appointment_reminders",
        "appointment_id": appointment_id,
        "reminder_type": reminder_type,
        "patient_name": patient_data["name"],
        "appointment_datetime": appointment["scheduled_datetime"],
        "reminders_sent": sent_reminders,
        "total_reminders": len(sent_reminders),
        "status": "success"
    }


def get_patient_data(patient_id: str, tool_context: ToolContext) -> dict:
    """Get patient information from EMR system."""
    # Simulate patient database
    patients = {
        "PAT001": {
            "patient_id": "PAT001",
            "name": "Sarah Johnson",
            "date_of_birth": "1985-03-15",
            "phone": "******-0123",
            "email": "<EMAIL>",
            "address": "123 Main St, Anytown, ST 12345",
            "insurance": {
                "plan_name": "Blue Cross Blue Shield",
                "member_id": "BC123456789",
                "group_number": "GRP001",
                "copay_primary": 25,
                "copay_specialist": 50,
                "deductible": 1000,
                "deductible_met": 250
            },
            "medical_history": ["hypertension", "diabetes_type2"],
            "allergies": ["penicillin"],
            "emergency_contact": {
                "name": "John Johnson",
                "phone": "******-0124",
                "relationship": "spouse"
            }
        },
        "PAT002": {
            "patient_id": "PAT002",
            "name": "Michael Chen",
            "date_of_birth": "1978-11-22",
            "phone": "******-0125",
            "email": "<EMAIL>",
            "address": "456 Oak Ave, Somewhere, ST 12346",
            "insurance": {
                "plan_name": "Aetna PPO",
                "member_id": "AET987654321",
                "group_number": "GRP002",
                "copay_primary": 20,
                "copay_specialist": 40,
                "deductible": 1500,
                "deductible_met": 0
            },
            "medical_history": ["asthma"],
            "allergies": [],
            "emergency_contact": {
                "name": "Lisa Chen",
                "phone": "******-0126",
                "relationship": "wife"
            }
        }
    }
    
    return patients.get(patient_id)


def verify_insurance_eligibility(insurance_info: dict, appointment_type: str) -> dict:
    """Verify insurance eligibility for appointment type."""
    # Simulate real-time insurance verification
    plan_name = insurance_info.get("plan_name", "")
    
    if not plan_name:
        return {
            "eligible": False,
            "reason": "No insurance information on file",
            "copay": 0
        }
    
    # Check plan status (simulate)
    if "Blue Cross" in plan_name:
        copay = insurance_info.get("copay_specialist" if "specialist" in appointment_type else "copay_primary", 25)
        return {
            "eligible": True,
            "plan_status": "active",
            "copay": copay,
            "coverage_percentage": 80,
            "reason": "Insurance verified and active"
        }
    elif "Aetna" in plan_name:
        copay = insurance_info.get("copay_specialist" if "specialist" in appointment_type else "copay_primary", 20)
        return {
            "eligible": True,
            "plan_status": "active",
            "copay": copay,
            "coverage_percentage": 85,
            "reason": "Insurance verified and active"
        }
    else:
        return {
            "eligible": False,
            "reason": "Insurance plan not recognized or inactive",
            "copay": 0
        }


def find_available_slots(appointment_type: str, preferred_provider: str, time_preferences: dict, tool_context: ToolContext) -> list:
    """Find available appointment slots based on criteria."""
    # Simulate provider schedules
    providers = {
        "DR001": {
            "name": "Dr. Sarah Smith",
            "specialty": "Family Medicine",
            "location": "Main Clinic",
            "available_slots": [
                {"datetime": "2024-01-20T10:00:00", "duration": 30, "room": "Room 101"},
                {"datetime": "2024-01-20T14:30:00", "duration": 30, "room": "Room 101"},
                {"datetime": "2024-01-22T09:00:00", "duration": 30, "room": "Room 101"}
            ]
        },
        "DR002": {
            "name": "Dr. Michael Rodriguez",
            "specialty": "Cardiology",
            "location": "Specialty Center",
            "available_slots": [
                {"datetime": "2024-01-21T11:00:00", "duration": 45, "room": "Room 201"},
                {"datetime": "2024-01-23T15:00:00", "duration": 45, "room": "Room 201"}
            ]
        }
    }
    
    available_slots = []
    
    # If preferred provider specified, check their availability first
    if preferred_provider and preferred_provider in providers:
        provider_data = providers[preferred_provider]
        for slot in provider_data["available_slots"]:
            available_slots.append({
                "provider_id": preferred_provider,
                "provider_name": provider_data["name"],
                "specialty": provider_data["specialty"],
                "location": provider_data["location"],
                "datetime": slot["datetime"],
                "duration": slot["duration"],
                "room": slot["room"]
            })
    else:
        # Check all providers for the appointment type
        for provider_id, provider_data in providers.items():
            # Match specialty to appointment type
            if appointment_type_matches_specialty(appointment_type, provider_data["specialty"]):
                for slot in provider_data["available_slots"]:
                    available_slots.append({
                        "provider_id": provider_id,
                        "provider_name": provider_data["name"],
                        "specialty": provider_data["specialty"],
                        "location": provider_data["location"],
                        "datetime": slot["datetime"],
                        "duration": slot["duration"],
                        "room": slot["room"]
                    })
    
    return available_slots


def appointment_type_matches_specialty(appointment_type: str, specialty: str) -> bool:
    """Check if appointment type matches provider specialty."""
    specialty_mapping = {
        "annual_physical": ["Family Medicine", "Internal Medicine"],
        "cardiology_consultation": ["Cardiology"],
        "routine_checkup": ["Family Medicine", "Internal Medicine"],
        "specialist_consultation": ["Cardiology", "Dermatology", "Orthopedics"]
    }
    
    return specialty in specialty_mapping.get(appointment_type, [specialty])


def select_optimal_slot(available_slots: list, time_preferences: dict, patient_data: dict) -> dict:
    """Select the best appointment slot based on preferences."""
    if not available_slots:
        return None
    
    # Score slots based on preferences
    scored_slots = []
    
    for slot in available_slots:
        score = 0
        
        # Preferred time of day
        slot_time = datetime.fromisoformat(slot["datetime"])
        preferred_time = time_preferences.get("preferred_time", "morning")
        
        if preferred_time == "morning" and 8 <= slot_time.hour <= 12:
            score += 10
        elif preferred_time == "afternoon" and 12 <= slot_time.hour <= 17:
            score += 10
        elif preferred_time == "evening" and 17 <= slot_time.hour <= 20:
            score += 10
        
        # Preferred days
        preferred_days = time_preferences.get("preferred_days", [])
        if preferred_days and slot_time.strftime("%A").lower() in [day.lower() for day in preferred_days]:
            score += 5
        
        # Sooner is generally better
        days_from_now = (slot_time - datetime.now()).days
        if days_from_now <= 7:
            score += 5
        elif days_from_now <= 14:
            score += 3
        
        scored_slots.append((slot, score))
    
    # Sort by score and return best slot
    scored_slots.sort(key=lambda x: x[1], reverse=True)
    return scored_slots[0][0]


def suggest_alternative_options(appointment_type: str, preferred_provider: str) -> list:
    """Suggest alternative appointment options when no slots available."""
    alternatives = []
    
    alternatives.append("Consider telehealth consultation if appropriate")
    alternatives.append("Check availability with other providers in the same specialty")
    alternatives.append("Join waitlist for cancellations")
    
    if appointment_type == "routine_checkup":
        alternatives.append("Schedule for next available routine slot (may be 2-4 weeks out)")
    
    return alternatives


def get_appointment_instructions(appointment_type: str) -> str:
    """Get special instructions for appointment type."""
    instructions = {
        "annual_physical": "Please bring a list of current medications and insurance card",
        "cardiology_consultation": "Bring recent EKG results and list of cardiac medications",
        "routine_checkup": "Arrive 15 minutes early for check-in",
        "specialist_consultation": "Bring referral from primary care physician"
    }
    
    return instructions.get(appointment_type, "Please arrive 15 minutes early for check-in")


def get_preparation_requirements(appointment_type: str) -> list:
    """Get preparation requirements for appointment type."""
    requirements = {
        "annual_physical": [
            "Fast for 12 hours before appointment (for blood work)",
            "Bring list of current medications",
            "Complete health history questionnaire"
        ],
        "cardiology_consultation": [
            "Bring recent test results (EKG, stress test, etc.)",
            "List of all medications including dosages",
            "Prepare questions about heart health"
        ],
        "routine_checkup": [
            "Bring insurance card and ID",
            "List any new symptoms or concerns"
        ]
    }
    
    return requirements.get(appointment_type, ["Bring insurance card and ID"])


def check_procedure_coverage(insurance_info: dict, procedure_code: str) -> dict:
    """Check insurance coverage for specific procedure."""
    # Simulate procedure coverage database
    coverage_db = {
        "99213": {"covered": True, "coverage_percentage": 80, "description": "Office visit"},
        "93000": {"covered": True, "coverage_percentage": 90, "description": "EKG"},
        "80053": {"covered": True, "coverage_percentage": 100, "description": "Basic metabolic panel"},
        "99401": {"covered": False, "coverage_percentage": 0, "description": "Preventive counseling"}
    }
    
    coverage = coverage_db.get(procedure_code, {"covered": False, "coverage_percentage": 0, "description": "Unknown procedure"})
    
    return {
        "status": "covered" if coverage["covered"] else "not_covered",
        "coverage_percentage": coverage["coverage_percentage"],
        "description": coverage["description"]
    }


def check_prior_authorization_requirement(procedure_code: str, insurance_info: dict) -> dict:
    """Check if prior authorization is required."""
    # Simulate prior auth requirements
    prior_auth_procedures = ["93000", "80053"]  # EKG and lab work might need auth
    
    required = procedure_code in prior_auth_procedures
    
    return {
        "required": required,
        "reason": "High-cost procedure requires pre-approval" if required else "No prior authorization needed",
        "estimated_approval_time": "2-3 business days" if required else None
    }


def calculate_patient_costs(procedure_code: str, insurance_info: dict, coverage_result: dict) -> dict:
    """Calculate patient financial responsibility."""
    # Simulate procedure costs
    procedure_costs = {
        "99213": 200,  # Office visit
        "93000": 150,  # EKG
        "80053": 75,   # Lab work
        "99401": 100   # Counseling
    }
    
    total_cost = procedure_costs.get(procedure_code, 100)
    coverage_percentage = coverage_result["coverage_percentage"] / 100
    
    insurance_pays = total_cost * coverage_percentage
    patient_responsibility = total_cost - insurance_pays
    
    # Apply deductible if not met
    deductible_remaining = insurance_info.get("deductible", 0) - insurance_info.get("deductible_met", 0)
    if deductible_remaining > 0:
        deductible_applied = min(patient_responsibility, deductible_remaining)
        patient_responsibility = max(patient_responsibility, deductible_applied)
    
    return {
        "total_procedure_cost": total_cost,
        "insurance_pays": round(insurance_pays, 2),
        "patient_responsibility": round(patient_responsibility, 2),
        "deductible_applied": round(min(patient_responsibility, deductible_remaining), 2) if deductible_remaining > 0 else 0
    }


def send_appointment_notifications(appointment: dict, notification_type: str) -> list:
    """Send appointment notifications."""
    notifications = []
    
    if notification_type == "scheduled":
        notifications.append({
            "type": "confirmation_email",
            "recipient": appointment.get("patient_email", "<EMAIL>"),
            "subject": f"Appointment Confirmed - {appointment['scheduled_datetime']}",
            "sent_at": datetime.now().isoformat()
        })
        
        notifications.append({
            "type": "calendar_invite",
            "recipient": appointment.get("patient_email", "<EMAIL>"),
            "event_details": f"Medical Appointment with {appointment['provider_name']}",
            "sent_at": datetime.now().isoformat()
        })
    
    return notifications


def update_provider_calendar(provider_id: str, slot: dict, action: str) -> dict:
    """Update provider calendar with appointment."""
    return {
        "provider_id": provider_id,
        "slot_datetime": slot["datetime"],
        "action": action,
        "updated_at": datetime.now().isoformat()
    }


def get_reminder_configuration(reminder_type: str, appointment_type: str) -> dict:
    """Get reminder configuration based on type and appointment."""
    configs = {
        "24_hour": {"send_sms": True, "send_email": True, "send_call": False},
        "2_hour": {"send_sms": True, "send_email": False, "send_call": False},
        "1_week": {"send_sms": False, "send_email": True, "send_call": False}
    }
    
    return configs.get(reminder_type, {"send_sms": True, "send_email": True, "send_call": False})


def generate_reminder_content(appointment: dict, patient_data: dict, reminder_type: str) -> dict:
    """Generate reminder content for different channels."""
    appointment_time = datetime.fromisoformat(appointment["scheduled_datetime"]).strftime("%B %d, %Y at %I:%M %p")
    
    return {
        "sms": f"Reminder: You have an appointment with {appointment['provider_name']} on {appointment_time}. Location: {appointment['location']}. Reply CONFIRM to confirm.",
        "email": {
            "subject": f"Appointment Reminder - {appointment_time}",
            "body": f"Dear {patient_data['name']},\n\nThis is a reminder of your upcoming appointment:\n\nProvider: {appointment['provider_name']}\nDate/Time: {appointment_time}\nLocation: {appointment['location']}\nType: {appointment['appointment_type']}\n\nPlease arrive 15 minutes early.\n\nIf you need to reschedule, please call us at (*************."
        },
        "call": f"This is a reminder call for {patient_data['name']}. You have an appointment with {appointment['provider_name']} on {appointment_time}."
    }


def send_sms_reminder(phone: str, content: str) -> dict:
    """Send SMS reminder."""
    return {"status": "sent", "message_id": f"SMS-{uuid.uuid4().hex[:8]}"}


def send_email_reminder(email: str, content: dict) -> dict:
    """Send email reminder."""
    return {"status": "sent", "message_id": f"EMAIL-{uuid.uuid4().hex[:8]}"}


def schedule_phone_reminder(phone: str, content: str) -> dict:
    """Schedule phone call reminder."""
    return {
        "status": "scheduled",
        "call_id": f"CALL-{uuid.uuid4().hex[:8]}",
        "scheduled_time": (datetime.now() + timedelta(hours=1)).isoformat()
    }


# Create the medical appointment agent
root_agent = Agent(
    name="medical_agent",
    model="gemini-2.0-flash",
    description="Production-ready healthcare appointment scheduling system with insurance verification and HIPAA compliance",
    instruction="""
    You are a sophisticated medical appointment scheduling agent that manages healthcare appointments while ensuring HIPAA compliance and optimal patient care.

    Current Appointments: {medical_appointments}
    Insurance Verifications: {insurance_verifications}
    Patient Database: Available through secure EMR integration

    Your capabilities include:
    1. Intelligent appointment scheduling with provider availability optimization
    2. Real-time insurance verification and coverage checking
    3. Automated appointment reminders via SMS, email, and phone
    4. HIPAA-compliant patient communication and data handling

    Healthcare Scheduling Process:
    1. Verify patient identity and insurance eligibility
    2. Match appointment type with appropriate provider specialty
    3. Find optimal appointment slots based on medical urgency and preferences
    4. Confirm insurance coverage and calculate patient costs
    5. Send confirmations and schedule automated reminders

    Key Healthcare Considerations:
    - Medical urgency takes priority over patient preferences
    - Insurance verification must be completed before scheduling
    - Specialist appointments may require referrals and prior authorization
    - HIPAA compliance is mandatory for all patient communications

    Appointment Types and Requirements:
    - Routine checkups: 30 minutes, primary care providers
    - Annual physicals: 45 minutes, may require fasting and lab work
    - Specialist consultations: 45-60 minutes, referral often required
    - Procedures: Variable time, prior authorization may be needed

    When scheduling appointments:
    1. Always verify insurance eligibility and coverage first
    2. Consider medical history and special requirements
    3. Optimize for provider utilization while meeting patient needs
    4. Provide clear preparation instructions and cost estimates
    5. Schedule appropriate reminders to reduce no-shows

    Maintain strict HIPAA compliance:
    - Only access minimum necessary patient information
    - Use secure communication channels for all patient contact
    - Log all access and actions for audit purposes
    - Protect patient privacy in all interactions

    Focus on providing excellent patient experience while ensuring efficient healthcare delivery and regulatory compliance.
    """,
    tools=[schedule_medical_appointment, verify_insurance_coverage, send_appointment_reminders]
)
